{"private": true, "name": "openai-agents-js", "version": "0.0.1", "scripts": {"clean": "tsc-multi --clean", "prebuild": "pnpm -F @openai/* -r prebuild", "build": "tsc-multi", "postbuild": "pnpm -r -F @openai/* bundle", "packages:dev": "tsc-multi --watch", "docs:dev": "pnpm -F docs dev", "docs:translate": "pnpm -F docs translate", "docs:build": "pnpm -F docs build", "test": "CI=1 NODE_ENV=test vitest", "test:coverage": "NODE_ENV=test vitest run --coverage", "test:examples": "pnpm -r build-check", "test:integration": "NODE_ENV=test vitest run --config=vitest.integration.config.ts", "test:watch": "NODE_ENV=test vitest --watch", "dev": "tsx scripts/dev.mts", "lint": "eslint", "lint:fix": "eslint --fix", "examples:basic": "pnpm -F basic start", "examples:agents-as-tools": "pnpm -F agent-patterns start:agents-as-tools", "examples:deterministic": "pnpm -F agent-patterns start:deterministic", "examples:parallelization": "pnpm -F agent-patterns start:parallelization", "examples:human-in-the-loop": "pnpm -F agent-patterns start:human-in-the-loop", "examples:input-guardrails": "pnpm -F agent-patterns start:input-guardrails", "examples:output-guardrails": "pnpm -F agent-patterns start:output-guardrails", "examples:streamed": "pnpm -F agent-patterns start:streamed", "examples:streamed:human-in-the-loop": "pnpm -F agent-patterns start:human-in-the-loop-stream", "examples:routing": "pnpm -F agent-patterns start:routing", "examples:customer-service": "pnpm -F customer-service start", "examples:realtime-demo": "pnpm -F realtime-demo dev", "examples:realtime-next": "pnpm -F realtime-next dev", "examples:research-bot": "pnpm -F research-bot start", "examples:financial-research-agent": "pnpm -F financial-research-agent start", "examples:tools-computer-use": "pnpm -F tools start:computer-use", "examples:tools-file-search": "pnpm -F tools start:file-search", "examples:tools-web-search": "pnpm -F tools start:web-search", "examples:tool-filter": "tsx examples/mcp/tool-filter-example.ts", "ci:publish": "pnpm publish -r --no-git-checks", "bump-version": "changeset version && pnpm -F @openai/* prebuild", "prepare": "husky", "clear:deps": "rm -rf node_modules && pnpm -r exec rm -rf node_modules", "local-npm:reset": "rm -rf .cache/verdaccio/storage", "local-npm:start": "verdaccio --config verdaccio-config.yml", "local-npm:publish": "pnpm -r publish --registry http://localhost:4873 --force --no-git-checks"}, "devDependencies": {"@changesets/cli": "^2.29.5", "@eslint/js": "^9.30.1", "@types/node": "^22.16.3", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitest/coverage-v8": "^3.2.4", "concurrently": "^9.2.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-unused-imports": "^4.1.4", "execa": "^9.6.0", "husky": "^9.1.7", "playwright": "^1.54.0", "prettier": "^3.6.2", "rimraf": "^6.0.1", "tsc-multi": "^1.1.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "verdaccio": "^6.1.5", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.15.0"}