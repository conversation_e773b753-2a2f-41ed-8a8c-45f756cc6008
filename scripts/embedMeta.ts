import { readFileSync, writeFileSync } from 'node:fs';
import { resolve } from 'node:path';
import { cwd } from 'node:process';

const packageJson = JSON.parse(
  readFileSync(resolve(cwd(), 'package.json'), 'utf-8'),
);

const dependencies = Object.entries(packageJson.dependencies);
const openaiDependencies = dependencies.filter(
  ([name]) => name.startsWith('@openai/') || name === 'openai',
);

const versions = {
  [packageJson.name]: packageJson.version,
  ...Object.fromEntries(
    openaiDependencies.map(([name, version]) => [name, version]),
  ),
};

const METADATA = {
  name: packageJson.name,
  version: packageJson.version,
  versions: versions,
};

const output = `
// This file is automatically generated

export const METADATA = ${JSON.stringify(METADATA, null, 2)};

export default METADATA;
`;

writeFileSync(resolve(cwd(), 'src/metadata.ts'), output, 'utf-8');
