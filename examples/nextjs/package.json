{"name": "nextjs", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "build-check": "tsc --noEmit"}, "dependencies": {"@openai/agents": "workspace:*", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.80.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.515.0", "next": "15.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "wavtools": "^0.1.5", "zod": "^3.25.40"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}