{"private": true, "name": "mcp", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "@openai/agents": "workspace:*", "zod": "^3.25.40"}, "scripts": {"build-check": "tsc --noEmit", "start:stdio": "tsx filesystem-example.ts", "start:streamable-http": "tsx streamable-http-example.ts", "start:hosted-mcp-on-approval": "tsx hosted-mcp-on-approval.ts", "start:hosted-mcp-human-in-the-loop": "tsx hosted-mcp-human-in-the-loop.ts", "start:hosted-mcp-simple": "tsx hosted-mcp-simple.ts", "start:tool-filter": "tsx tool-filter-example.ts", "start:sse": "tsx sse-example.ts", "start:get-all-tools": "tsx get-all-mcp-tools-example.ts"}}