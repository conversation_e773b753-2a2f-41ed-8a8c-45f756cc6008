### Summary

<!-- Please give a short summary of the change and the problem this solves. -->

### Test plan

<!-- Please explain how this was tested -->

### Issue number

<!-- For example: "Closes #1234" -->

### Checks

- [ ] I've added new tests (if relevant)
- [ ] I've added/updated the relevant documentation
- [ ] I've run `pnpm test` and `pnpm test:examples`
  - [ ] (If you made a major change) I've run `pnpm test:integration` [(see details)](https://github.com/openai/openai-agents-js/tree/main/integration-tests)
- [ ] I've made sure tests pass
- [ ] I've added a changeset using `pnpm changeset` to indicate my changes
