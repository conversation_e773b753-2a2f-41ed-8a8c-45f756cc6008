---
title: Handoffs
description: Delegate tasks from one agent to another
---

import { Code } from '@astrojs/starlight/components';
import basicUsageExample from '../../../../../examples/docs/handoffs/basicUsage.ts?raw';
import customizeHandoffExample from '../../../../../examples/docs/handoffs/customizeHandoff.ts?raw';
import handoffInputExample from '../../../../../examples/docs/handoffs/handoffInput.ts?raw';
import inputFilterExample from '../../../../../examples/docs/handoffs/inputFilter.ts?raw';
import recommendedPromptExample from '../../../../../examples/docs/handoffs/recommendedPrompt.ts?raw';

Handoffs let an agent delegate part of a conversation to another agent. This is useful when different agents specialise in specific areas. In a customer support app for example, you might have agents that handle bookings, refunds or FAQs.

Handoffs are represented as tools to the LLM. If you hand off to an agent called `Refund Agent`, the tool name would be `transfer_to_refund_agent`.

## Creating a handoff

Every agent accepts a `handoffs` option. It can contain other `Agent` instances or `Handoff` objects returned by the `handoff()` helper.

### Basic usage

<Code lang="typescript" code={basicUsageExample} title="Basic handoffs" />

### Customising handoffs via `handoff()`

The `handoff()` function lets you tweak the generated tool.

- `agent` – the agent to hand off to.
- `toolNameOverride` – override the default `transfer_to_<agent_name>` tool name.
- `toolDescriptionOverride` – override the default tool description.
- `onHandoff` – callback when the handoff occurs. Receives a `RunContext` and optionally parsed input.
- `inputType` – expected input schema for the handoff.
- `inputFilter` – filter the history passed to the next agent.

<Code
  lang="typescript"
  code={customizeHandoffExample}
  title="Customized handoffs"
/>

## Handoff inputs

Sometimes you want the LLM to provide data when invoking a handoff. Define an input schema and use it in `handoff()`.

<Code lang="typescript" code={handoffInputExample} title="Handoff inputs" />

## Input filters

By default a handoff receives the entire conversation history. To modify what gets passed to the next agent, provide an `inputFilter`.
Common helpers live in `@openai/agents-core/extensions`.

<Code lang="typescript" code={inputFilterExample} title="Input filters" />

## Recommended prompts

LLMs respond more reliably when your prompts mention handoffs. The SDK exposes a recommended prefix via `RECOMMENDED_PROMPT_PREFIX`.

<Code
  lang="typescript"
  code={recommendedPromptExample}
  title="Recommended prompts"
/>
