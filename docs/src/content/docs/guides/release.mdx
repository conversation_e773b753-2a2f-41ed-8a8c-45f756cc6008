---
title: Release process
description: Learn how we version and release the SDK and recent changes.
---

## Versioning

The project follows a slightly modified version of semantic versioning using the form `0.Y.Z`. The leading `0` indicates the SDK is still evolving rapidly. Increment the components as follows:

## Minor (`Y`) versions

We will increase minor versions `Y` for **breaking changes** to any public interfaces that are not marked as beta. For example, going from `0.0.x` to `0.1.x` might include breaking changes.

If you don't want breaking changes, we recommend pinning to `0.0.x` versions in your project.

## Patch (`Z`) versions

We will increment `Z` for non-breaking changes:

- Bug fixes
- New features
- Changes to private interfaces
- Updates to beta features

## Versioning sub-packages

The main `@openai/agents` package is comprised of multiple sub-packages that can be used independently. At the moment the versions of the packages are linked, meaning if one package receives a version increase, so do the others. We might change this strategy as we move to `1.0.0`.

## Changelogs

We generate changelogs for each of the sub-packages to help understand what has changed. As the changes might have happened in a sub-package, you might have to look in that respective changelog for details on the change.

- [`@openai/agents`](https://github.com/openai/openai-agents-js/blob/main/packages/agents/CHANGELOG.md)
- [`@openai/agents-core`](https://github.com/openai/openai-agents-js/blob/main/packages/agents-core/CHANGELOG.md)
- [`@openai/agents-extensions`](https://github.com/openai/openai-agents-js/blob/main/packages/agents-extensions/CHANGELOG.md)
- [`@openai/agents-openai`](https://github.com/openai/openai-agents-js/blob/main/packages/agents-openai/CHANGELOG.md)
- [`@openai/agents-realtime`](https://github.com/openai/openai-agents-js/blob/main/packages/agents-realtime/CHANGELOG.md)
