---
title: Using Realtime Agents with Twilio
description: Connect your Agents SDK agents to Twilio to use voice agents
---

import { Aside, Steps, Code } from '@astrojs/starlight/components';
import twilioBasicExample from '../../../../../examples/docs/extensions/twilio-basic.ts?raw';
import twilioServerExample from '../../../../../examples/realtime-twilio/index.ts?raw';

Twilio offers a [Media Streams API](https://www.twilio.com/docs/voice/media-streams) that sends the
raw audio from a phone call to a WebSocket server. This set up can be used to connect your
[voice agents](/openai-agents-js/guides/voice-agents) to Twilio. You can use the default Realtime Session transport
in `websocket` mode to connect the events coming from Twilio to your Realtime Session. However,
this requires you to set the right audio format and adjust your own interruption timing as phone
calls will naturally introduce more latency than a web-based conversation.

To improve the set up experience, we've created a dedicated transport layer that handles the
connection to Twilio for you, including handling interruptions and audio forwarding for you.

<Aside type="caution">
  This adapter is still in beta. You may run into edge case issues or bugs.
  Please report any issues via [GitHub
  issues](https://github.com/openai/openai-agents-js/issues) and we'll fix
  quickly.
</Aside>

## Setup

<Steps>

1. **Make sure you have a Twilio account and a Twilio phone number.**

2. **Set up a WebSocket server that can receive events from Twilio.**

   If you are developing locally, this will require you to configure a local tunnel like
   this will require you to configure a local tunnel like [`ngrok`](https://ngrok.io/) or
   [Cloudflare Tunnel](https://developers.cloudflare.com/pages/how-to/preview-with-cloudflare-tunnel/)
   to make your local server accessible to Twilio. You can use the `TwilioRealtimeTransportLayer`
   to connect to Twilio.

3. **Install the Twilio adapter by installing the extensions package:**

   ```bash
   npm install @openai/agents-extensions
   ```

4. **Import the adapter and model to connect to your `RealtimeSession`:**

   <Code
     lang="typescript"
     code={twilioBasicExample.replace(
       /\n\s+\/\/ @ts-expect-error - this is not defined/g,
       '',
     )}
   />

5. **Connect your `RealtimeSession` to Twilio:**

   ```typescript
   session.connect({ apiKey: 'your-openai-api-key' });
   ```

</Steps>

Any event and behavior that you would expect from a `RealtimeSession` will work as expected
including tool calls, guardrails, and more. Read the [voice agents guide](/openai-agents-js/guides/voice-agents)
for more information on how to use the `RealtimeSession` with voice agents.

## Tips and Considerations

1. **Speed is the name of the game.**

   In order to receive all the necessary events and audio from Twilio, you should create your
   `TwilioRealtimeTransportLayer` instance as soon as you have a reference to the WebSocket
   connection and immediately call `session.connect()` afterwards.

2. **Access the raw Twilio events.**

   If you want to access the raw events that are being sent by Twilio, you can listen to the
   `transport_event` event on your `RealtimeSession` instance. Every event from Twilio will have a
   type of `twilio_message` and a `message` property that contains the raw event data.

3. **Watch debug logs.**

   Sometimes you may run into issues where you want more information on what's going on. Using
   a `DEBUG=openai-agents*` environment variable will show all the debug logs from the Agents SDK.
   Alternatively, you can enable just debug logs for the Twilio adapter using
   `DEBUG=openai-agents:extensions:twilio*`.

## Full example server

Below is an example of a full end-to-end example of a WebSocket server that receives requests from
Twilio and forwards them to a `RealtimeSession`.

<Code
  lang="typescript"
  code={twilioServerExample}
  title="Example server using Fastify"
/>
