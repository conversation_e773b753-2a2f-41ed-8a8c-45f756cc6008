---
title: MCP 連携
description: Learn how to utilize MCP servers as tools
---

import { Code } from '@astrojs/starlight/components';
import hostedAgentExample from '../../../../../../examples/docs/mcp/hostedAgent.ts?raw';
import hostedExample from '../../../../../../examples/docs/mcp/hosted.ts?raw';
import hostedStreamExample from '../../../../../../examples/docs/mcp/hostedStream.ts?raw';
import hostedHITLExample from '../../../../../../examples/docs/mcp/hostedHITL.ts?raw';
import streamableHttpExample from '../../../../../../examples/docs/mcp/streamableHttp.ts?raw';
import stdioExample from '../../../../../../examples/docs/mcp/stdio.ts?raw';
import toolFilterExample from '../../../../../../examples/docs/mcp/tool-filter.ts?raw';

[**Model Context Protocol (MCP)**](https://modelcontextprotocol.io) は、アプリケーションが LLM にツールやコンテキストを提供する方法を標準化するオープンなプロトコルです。MCP のドキュメントより引用します。

> MCP は、アプリケーションが LLM にコンテキストを提供する方法を標準化するオープンなプロトコルです。MCP を AI アプリケーション向けの USB‑C ポートだと考えてください。USB‑C がデバイスをさまざまな周辺機器やアクセサリーに接続する標準化された方法を提供するのと同様に、MCP は AI モデルを異なるデータソースやツールに接続する標準化された方法を提供します。

この SDK がサポートする MCP サーバーには 3 種類あります。

1. **Hosted MCP server tools** – [OpenAI Responses API](https://platform.openai.com/docs/guides/tools-remote-mcp) によってツールとして利用されるリモート MCP サーバー
2. **Streamable HTTP MCP servers** – [Streamable HTTP transport](https://modelcontextprotocol.io/docs/concepts/transports#streamable-http) を実装するローカルまたはリモートのサーバー
3. **Stdio MCP servers** – 標準入出力経由でアクセスするサーバー（最も簡単な選択肢）

ユースケースに応じてサーバータイプを選択してください。

| 必要なこと                                                                           | 推奨オプション          |
| ------------------------------------------------------------------------------------ | ----------------------- |
| 公開アクセス可能なリモートサーバーを既定の OpenAI responses モデルで呼び出したい     | **1. Hosted MCP tools** |
| 公開アクセス可能なリモートサーバーを使うが、ツール呼び出しはローカルでトリガーしたい | **2. Streamable HTTP**  |
| ローカルで動作する Streamable HTTP サーバーを使いたい                                | **2. Streamable HTTP**  |
| OpenAI‑Responses 以外のモデルで任意の Streamable HTTP サーバーを使いたい             | **2. Streamable HTTP**  |
| 標準入出力プロトコルのみをサポートするローカル MCP サーバーと連携したい              | **3. Stdio**            |

## 1. Hosted MCP server tools

Hosted ツールは、往復の処理全体をモデル側に任せます。あなたのコードが MCP サーバーを呼び出す代わりに、OpenAI Responses API がリモートのツールエンドポイントを呼び出し、その結果をモデルへストリーミングします。

以下は hosted MCP ツールを使う最も簡単な例です。リモート MCP サーバーのラベルと URL を `hostedMcpTool` ユーティリティ関数に渡すことで、hosted MCP サーバーツールを簡単に作成できます。

<Code lang="typescript" code={hostedAgentExample} title="hostedAgent.ts" />

次に、`run` 関数（または独自にカスタマイズした `Runner` インスタンスの `run` メソッド）でエージェントを実行できます。

<Code
  lang="typescript"
  code={hostedExample}
  title="Run with hosted MCP tools"
/>

増分的な MCP の結果をストリーミングするには、`Agent` を実行するときに `stream: true` を渡します。

<Code
  lang="typescript"
  code={hostedStreamExample}
  title="Run with hosted MCP tools (streaming)"
/>

#### オプションの承認フロー

センシティブな操作に対しては、個々のツール呼び出しに人間の承認を要求できます。`requireApproval: 'always'` または、ツール名を `'never'`/`'always'` にマッピングするきめ細かなオブジェクトを渡します。

ツール呼び出しの安全性をプログラム的に判断できる場合は、[`onApproval` コールバック](https://github.com/openai/openai-agents-js/blob/main/examples/mcp/hosted-mcp-on-approval.ts) を使って承認または却下できます。人による承認が必要な場合は、ローカルの 関数ツール と同様に、`interruptions` を用いた同じ [人間の介入（HITL）](/openai-agents-js/ja/guides/human-in-the-loop/) の手法を使えます。

<Code
  lang="typescript"
  code={hostedHITLExample}
  title="Human in the loop with hosted MCP tools"
/>

完全に動作するサンプル（Hosted ツール/Streamable HTTP/stdio + Streaming、HITL、onApproval）は、GitHub リポジトリの [examples/mcp](https://github.com/openai/openai-agents-js/tree/main/examples/mcp) にあります。

## 2. Streamable HTTP MCP servers

エージェントがローカルまたはリモートの Streamable HTTP MCP サーバーと直接通信する場合は、サーバーの `url`、`name` と任意の設定で `MCPServerStreamableHttp` をインスタンス化します。

<Code
  lang="typescript"
  code={streamableHttpExample}
  title="Run with Streamable HTTP MCP servers"
/>

コンストラクタは、`authProvider`、`requestInit`、`fetch`、`reconnectionOptions`、`sessionId` などの MCP TypeScript‑SDK の追加オプションも受け付けます。詳細は [MCP TypeScript SDK リポジトリ](https://github.com/modelcontextprotocol/typescript-sdk) とそのドキュメントを参照してください。

## 3. Stdio MCP servers

標準入出力のみを公開するサーバーには、`fullCommand` を指定して `MCPServerStdio` をインスタンス化します。

<Code
  lang="typescript"
  code={stdioExample}
  title="Run with Stdio MCP servers"
/>

## その他の知識

**Streamable HTTP** と **Stdio** サーバーでは、`Agent` の実行ごとに利用可能なツールを発見するために `list_tools()` を呼ぶ場合があります。この往復は、とくにリモートサーバーではレイテンシーを増やす可能性があるため、`MCPServerStdio` または `MCPServerStreamableHttp` に `cacheToolsList: true` を渡して結果をメモリにキャッシュできます。

ツール一覧が変わらない確信がある場合のみ有効化してください。後でキャッシュを無効化するには、サーバーインスタンスで `invalidateToolsCache()` を呼び出します。

### ツールのフィルタリング

`createMCPToolStaticFilter` による静的フィルター、またはカスタム関数を渡すことで、各サーバーから公開するツールを制限できます。両方の手法を示す複合的な例を以下に示します。

<Code lang="typescript" code={toolFilterExample} title="Tool filtering" />

## 参考資料

- [Model Context Protocol](https://modelcontextprotocol.io/) – 公式仕様
- [examples/mcp](https://github.com/openai/openai-agents-js/tree/main/examples/mcp) – 上記で参照した実行可能なデモ
