---
title: ハンドオフ
description: Delegate tasks from one agent to another
---

import { Code } from '@astrojs/starlight/components';
import basicUsageExample from '../../../../../../examples/docs/handoffs/basicUsage.ts?raw';
import customizeHandoffExample from '../../../../../../examples/docs/handoffs/customizeHandoff.ts?raw';
import handoffInputExample from '../../../../../../examples/docs/handoffs/handoffInput.ts?raw';
import inputFilterExample from '../../../../../../examples/docs/handoffs/inputFilter.ts?raw';
import recommendedPromptExample from '../../../../../../examples/docs/handoffs/recommendedPrompt.ts?raw';

ハンドオフは、会話の一部を別のエージェントに委譲します。これは、異なるエージェントが特定分野に特化している場合に有用です。たとえばカスタマーサポートアプリでは、予約、返金、FAQ を扱うエージェントを分けられます。

ハンドオフは LLM に対してツールとして表現されます。`Refund Agent` にハンドオフする場合、ツール名は `transfer_to_refund_agent` になります。

## ハンドオフの作成

すべてのエージェントは `handoffs` オプションを受け付けます。ここには他の `Agent` インスタンス、または `handoff()` ヘルパーが返す `Handoff` オブジェクトを含められます。

### 基本的な使い方

<Code lang="typescript" code={basicUsageExample} title="Basic handoffs" />

### `handoff()` によるハンドオフのカスタマイズ

`handoff()` 関数で、生成されるツールを調整できます。

- `agent` – ハンドオフ先のエージェント
- `toolNameOverride` – 既定の `transfer_to_<agent_name>` ツール名を上書き
- `toolDescriptionOverride` – 既定のツール説明を上書き
- `onHandoff` – ハンドオフ発生時のコールバック。`RunContext` と、必要に応じてパース済み入力を受け取ります
- `inputType` – ハンドオフの想定入力スキーマ
- `inputFilter` – 次のエージェントへ渡す履歴のフィルター

<Code
  lang="typescript"
  code={customizeHandoffExample}
  title="Customized handoffs"
/>

## ハンドオフの入力

ハンドオフ呼び出し時に、LLM にデータを提供させたい場合があります。入力スキーマを定義し、`handoff()` で使用します。

<Code lang="typescript" code={handoffInputExample} title="Handoff inputs" />

## 入力フィルター

既定では、ハンドオフは会話履歴全体を受け取ります。次のエージェントへ渡す内容を変更するには、`inputFilter` を指定します。
一般的なヘルパーは `@openai/agents-core/extensions` にあります。

<Code lang="typescript" code={inputFilterExample} title="Input filters" />

## 推奨プロンプト

プロンプトにハンドオフへの言及があると、LLM はより安定して応答します。SDK は `RECOMMENDED_PROMPT_PREFIX` で推奨のプレフィックスを提供します。

<Code
  lang="typescript"
  code={recommendedPromptExample}
  title="Recommended prompts"
/>
