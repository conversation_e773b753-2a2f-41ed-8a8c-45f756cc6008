---
title: エージェントの実行
description: Configure and execute agent workflows with the Runner class
---

import { Aside, Code } from '@astrojs/starlight/components';
import helloWorldWithRunnerExample from '../../../../../../examples/docs/hello-world-with-runner.ts?raw';
import helloWorldExample from '../../../../../../examples/docs/hello-world.ts?raw';
import runningAgentsExceptionExample from '../../../../../../examples/docs/running-agents/exceptions1.ts?raw';
import chatLoopExample from '../../../../../../examples/docs/running-agents/chatLoop.ts?raw';

エージェントは単体では何もしません。`Runner` クラスまたは `run()` ユーティリティで実行します。

<Code lang="typescript" code={helloWorldExample} title="シンプルな実行" />

カスタムの runner が不要な場合は、シングルトンのデフォルト `Runner` インスタンスで動作する `run()` ユーティリティも使えます。

また、独自の Runner インスタンスを作成することもできます:

<Code
  lang="typescript"
  code={helloWorldWithRunnerExample}
  title="シンプルな実行"
/>

エージェントを実行すると、最終出力と実行の全履歴を含む [エージェントの実行結果](/openai-agents-js/ja/guides/results) オブジェクトを受け取ります。

## エージェントループ

Runner の run メソッドを使うとき、開始エージェントと入力を渡します。入力は文字列（ユーザーメッセージとして扱われます）または OpenAI Responses API のアイテムである入力アイテムのリストのいずれかです。

runner は次のループを実行します:

1. 現在の入力で現在のエージェントのモデルを呼び出す
2. LLM の応答を検査する
   - **最終出力** → 返す
   - **ハンドオフ** → 新しいエージェントに切り替え、蓄積された会話履歴を保持し、1 に戻る
   - **ツール呼び出し** → ツールを実行し、その結果を会話に追加して、1 に戻る
3. `maxTurns` に達したら [`MaxTurnsExceededError`](/openai-agents-js/openai/agents-core/classes/maxturnsexceedederror) を投げる

<Aside type="note">
  LLM
  出力が「最終出力」と見なされるルールは、望ましい型のテキスト出力を生成し、ツール呼び出しがない場合です。
</Aside>

### Runner のライフサイクル

アプリ起動時に `Runner` を作成し、リクエスト間で再利用します。インスタンスはモデルプロバイダーやトレーシングオプションなどのグローバル設定を保持します。まったく異なるセットアップが必要な場合のみ、別の `Runner` を作成してください。簡単なスクリプトでは、内部でデフォルト runner を使う `run()` を呼び出すこともできます。

## Run 引数

`run()` メソッドへの入力は、開始時のエージェント、実行の入力、そしてオプションの集合です。

入力は、文字列（ユーザーメッセージとして扱われます）、[input items](/openai-agents-js/openai/agents-core/type-aliases/agentinputitem) のリスト、または [Human in the loop (人間の介入)](/openai-agents-js/ja/guides/human-in-the-loop) エージェントを構築している場合は [`RunState`](/openai-agents-js/openai/agents-core/classes/runstate) オブジェクトのいずれかです。

追加オプションは次のとおりです:

| Option     | Default | Description                                                                                                                                        |
| ---------- | ------- | -------------------------------------------------------------------------------------------------------------------------------------------------- |
| `stream`   | `false` | `true` の場合、呼び出しは `StreamedRunResult` を返し、モデルから到着したイベントを逐次発行します                                                   |
| `context`  | –       | すべての tool / ガードレール / handoff に転送されるコンテキストオブジェクト。詳しくは[コンテキスト管理](/openai-agents-js/ja/guides/context)を参照 |
| `maxTurns` | `10`    | 安全上の上限。到達すると [`MaxTurnsExceededError`](/openai-agents-js/openai/agents-core/classes/maxturnsexceedederror) をスローします              |
| `signal`   | –       | キャンセル用の `AbortSignal`                                                                                                                       |

## ストリーミング

ストリーミングを使うと、LLM の実行中にストリーミングイベントを受け取れます。ストリームが開始されると、`StreamedRunResult` に実行に関する完全な情報（新しい出力を含む）が格納されます。`for await` ループでストリーミングイベントを反復処理できます。詳しくは[ストリーミング](/openai-agents-js/ja/guides/streaming)を参照してください。

## Run 設定

独自の `Runner` インスタンスを作成する場合は、runner を構成するために `RunConfig` オブジェクトを渡せます。

| Field                       | Type                  | Purpose                                                                          |
| --------------------------- | --------------------- | -------------------------------------------------------------------------------- |
| `model`                     | `string \| Model`     | 実行中のエージェント **すべて** に特定のモデルを強制します                       |
| `modelProvider`             | `ModelProvider`       | モデル名を解決します。デフォルトは OpenAI プロバイダーです                       |
| `modelSettings`             | `ModelSettings`       | エージェント個別設定を上書きするグローバルなチューニングパラメーター             |
| `handoffInputFilter`        | `HandoffInputFilter`  | handoff を行う際に入力アイテムを変換します（handoff 自体で定義されていない場合） |
| `inputGuardrails`           | `InputGuardrail[]`    | 最初のユーザー入力に適用されるガードレール                                       |
| `outputGuardrails`          | `OutputGuardrail[]`   | 最終出力に適用されるガードレール                                                 |
| `tracingDisabled`           | `boolean`             | OpenAI トレーシングを完全に無効化します                                          |
| `traceIncludeSensitiveData` | `boolean`             | スパンは発行しつつも、トレースから LLM/ツールの入出力を除外します                |
| `workflowName`              | `string`              | Traces ダッシュボードに表示され、関連する実行をグルーピングするのに役立ちます    |
| `traceId` / `groupId`       | `string`              | SDK に生成させず、トレース ID またはグループ ID を手動で指定します               |
| `traceMetadata`             | `Record<string, any>` | すべてのスパンに付与する任意のメタデータ                                         |

## 会話 / チャットスレッド

`runner.run()`（または `run()` ユーティリティ）への各呼び出しは、アプリケーションレベルの会話における 1 回の **ターン** を表します。エンドユーザーに `RunResult` のどこまでを見せるかは任意です。`finalOutput` のみの場合もあれば、生成されたアイテムすべての場合もあります。

<Code lang="typescript" code={chatLoopExample} title="会話履歴の引き継ぎ例" />

インタラクティブ版は[チャットのサンプル](https://github.com/openai/openai-agents-js/tree/main/examples/basic/chat.ts)を参照してください。

## 例外

SDK は捕捉可能な少数のエラーをスローします:

- [`MaxTurnsExceededError`](/openai-agents-js/openai/agents-core/classes/maxturnsexceedederror) – `maxTurns` に到達
- [`ModelBehaviorError`](/openai-agents-js/openai/agents-core/classes/modelbehaviorerror) – モデルが不正な出力を生成（例: 不正な JSON、未知のツール）
- [`InputGuardrailTripwireTriggered`](/openai-agents-js/openai/agents-core/classes/inputguardrailtripwiretriggered) / [`OutputGuardrailTripwireTriggered`](/openai-agents-js/openai/agents-core/classes/outputguardrailtripwiretriggered) – ガードレール違反
- [`GuardrailExecutionError`](/openai-agents-js/openai/agents-core/classes/guardrailexecutionerror) – ガードレールの実行に失敗
- [`ToolCallError`](/openai-agents-js/openai/agents-core/classes/toolcallerror) – いずれかの関数ツール呼び出しが失敗
- [`UserError`](/openai-agents-js/openai/agents-core/classes/usererror) – 設定またはユーザー入力に基づいて発生するエラー

これらはすべて基底の `AgentsError` クラスを継承しており、現在の実行状態にアクセスするための `state` プロパティを提供する場合があります。

`GuardrailExecutionError` を処理するサンプルコードは次のとおりです:

<Code
  lang="typescript"
  code={runningAgentsExceptionExample}
  title="ガードレール実行エラー"
/>

上記の例を実行すると、次の出力が表示されます:

```
Guardrail execution failed: Error: Input guardrail failed to complete: Error: Something is wrong!
Math homework guardrail tripped
```

---

## 次のステップ

- [モデル](/openai-agents-js/ja/guides/models)の構成方法を学ぶ
- エージェントに[ツール](/openai-agents-js/ja/guides/tools)を提供する
- 本番運用に向けて[ガードレール](/openai-agents-js/ja/guides/guardrails)や[トレーシング](/openai-agents-js/ja/guides/tracing)を追加する
