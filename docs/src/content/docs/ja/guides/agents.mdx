---
title: エージェント
description: Learn more about how to define agents in the OpenAI Agents SDK for JavaScript / TypeScript
---

import { Code } from '@astrojs/starlight/components';
import simpleAgent from '../../../../../../examples/docs/agents/simpleAgent.ts?raw';
import agentWithTools from '../../../../../../examples/docs/agents/agentWithTools.ts?raw';
import agentWithContext from '../../../../../../examples/docs/agents/agentWithContext.ts?raw';
import agentWithAodOutputType from '../../../../../../examples/docs/agents/agentWithAodOutputType.ts?raw';
import agentWithHandoffs from '../../../../../../examples/docs/agents/agentWithHandoffs.ts?raw';
import agentWithDynamicInstructions from '../../../../../../examples/docs/agents/agentWithDynamicInstructions.ts?raw';
import agentWithLifecycleHooks from '../../../../../../examples/docs/agents/agentWithLifecycleHooks.ts?raw';
import agentCloning from '../../../../../../examples/docs/agents/agentCloning.ts?raw';
import agentForcingToolUse from '../../../../../../examples/docs/agents/agentForcingToolUse.ts?raw';

エージェントは OpenAI Agents SDK の主要な構成要素です。**Agent** は、以下の設定を行った Large Language Model (LLM) です。

- **Instructions** – モデルに _自分が何者か_ と _どのように応答すべきか_ を伝えるシステムプロンプト
- **Model** – 呼び出す OpenAI モデル、および任意のモデル調整パラメーター
- **Tools** – LLM がタスク達成のために呼び出せる関数や API の一覧

<Code lang="typescript" code={simpleAgent} title="基本的なエージェント定義" />

このページでは、すべてのエージェント機能を詳しく説明します。

---

## 基本設定

`Agent` コンストラクターは 1 つの設定オブジェクトを受け取ります。よく使われるプロパティは次のとおりです。

| Property        | Required | Description                                                                                         |
| --------------- | -------- | --------------------------------------------------------------------------------------------------- |
| `name`          | yes      | 短い人間可読の識別子                                                                                |
| `instructions`  | yes      | システムプロンプト（文字列 **または** 関数 – [Dynamic instructions](#dynamic-instructions) を参照） |
| `model`         | no       | モデル名 **または** カスタムの [`Model`](/openai-agents-js/openai/agents/interfaces/model/) 実装    |
| `modelSettings` | no       | 調整パラメーター（temperature, top_p など）                                                         |
| `tools`         | no       | モデルが呼び出せる [`Tool`](/openai-agents-js/openai/agents/type-aliases/tool/) の配列              |

<Code lang="typescript" code={agentWithTools} title="ツール付きエージェント" />

---

## コンテキスト

エージェントは **コンテキスト型に対してジェネリック** です（例: `Agent<TContext, TOutput>`）。コンテキストは、あなたが作成して `Runner.run()` に渡す依存性注入オブジェクトです。すべてのツール、ガードレール、ハンドオフなどに転送され、状態の保存や共有サービス（データベース接続、ユーザーのメタデータ、フィーチャーフラグ、…）の提供に役立ちます。

<Code
  lang="typescript"
  code={agentWithContext}
  title="コンテキスト付きエージェント"
/>

---

## 出力タイプ

デフォルトでは、エージェントは **プレーンテキスト**（`string`）を返します。モデルに構造化オブジェクトを返させたい場合は、`outputType` プロパティを指定します。SDK は以下を受け付けます。

1. [Zod](https://github.com/colinhacks/zod) スキーマ（`z.object({...})`）
2. 任意の JSON‑schema 互換オブジェクト

<Code
  lang="typescript"
  code={agentWithAodOutputType}
  title="Zod による構造化出力"
/>

`outputType` が指定されると、SDK はプレーンテキストの代わりに自動的に
[structured outputs](https://platform.openai.com/docs/guides/structured-outputs) を使用します。

---

## ハンドオフ

エージェントは `handoffs` プロパティを介して、他のエージェントへ **委譲** できます。一般的なパターンとして、会話をより専門的なサブエージェントへルーティングする _トリアージ エージェント_ を使用します。

<Code
  lang="typescript"
  code={agentWithHandoffs}
  title="ハンドオフ付きエージェント"
/>

このパターンの詳細は [ハンドオフ](/openai-agents-js/ja/guides/handoffs) を参照してください。

---

## 動的 instructions

`instructions` は文字列の代わりに **関数** にできます。この関数は現在の `RunContext` とエージェントインスタンスを受け取り、文字列 _または_ `Promise<string>` を返せます。

<Code
  lang="typescript"
  code={agentWithDynamicInstructions}
  title="動的 instructions を使うエージェント"
/>

同期関数と `async` 関数のどちらにも対応しています。

---

## ライフサイクルフック

高度なユースケースでは、イベントをリッスンしてエージェントのライフサイクルを観測できます

<Code
  lang="typescript"
  code={agentWithLifecycleHooks}
  title="ライフサイクルフック付きエージェント"
/>

---

## ガードレール

ガードレールは、ユーザー入力やエージェント出力を検証・変換できます。`inputGuardrails` と `outputGuardrails` 配列で設定します。詳細は [ガードレール](/openai-agents-js/ja/guides/guardrails) を参照してください。

---

## エージェントのクローン／コピー

既存エージェントを少しだけ変更したバージョンが必要ですか？`clone()` メソッドを使うと、まったく新しい `Agent` インスタンスが返されます。

<Code lang="typescript" code={agentCloning} title="エージェントのクローン" />

---

## ツール使用の強制

ツールを提供しても、LLM が必ず呼び出すとは限りません。`modelSettings.tool_choice` でツール使用を **強制** できます。

1. `'auto'`（デフォルト）– ツールを使うかどうかは LLM が判断
2. `'required'` – LLM は _必ず_ ツールを呼び出す（どれを使うかは選択可）
3. `'none'` – LLM はツールを呼び出しては **いけない**
4. 特定のツール名（例: `'calculator'`）– LLM はそのツールを必ず呼び出す

<Code lang="typescript" code={agentForcingToolUse} title="ツール使用の強制" />

### 無限ループの防止

ツール呼び出し後、SDK は自動的に `tool_choice` を `'auto'` にリセットします。これにより、ツールを繰り返し呼び出そうとする無限ループを防ぎます。この動作は `resetToolChoice` フラグや `toolUseBehavior` の設定で上書きできます。

- `'run_llm_again'`（デフォルト）– ツールの結果で LLM をもう一度実行
- `'stop_on_first_tool'` – 最初のツール結果を最終回答として扱う
- `{ stopAtToolNames: ['my_tool'] }` – 指定ツールのいずれかが呼ばれたら停止
- `(context, toolResults) => ...` – 実行を終了すべきかを返すカスタム関数

```typescript
const agent = new Agent({
  ...,
  toolUseBehavior: 'stop_on_first_tool',
});
```

---

## 次のステップ

- [エージェントの実行](/openai-agents-js/ja/guides/running-agents) を学ぶ
- [ツール](/openai-agents-js/ja/guides/tools)、[ガードレール](/openai-agents-js/ja/guides/guardrails)、[モデル](/openai-agents-js/ja/guides/models) を掘り下げる
- サイドバーの **@openai/agents** の配下にある TypeDoc リファレンスを参照する
