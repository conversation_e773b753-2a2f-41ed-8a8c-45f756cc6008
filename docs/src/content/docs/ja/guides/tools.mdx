---
title: ツール
description: Provide your agents with capabilities via hosted tools or custom function tools
---

import { Code } from '@astrojs/starlight/components';
import toolsFunctionExample from '../../../../../../examples/docs/tools/functionTools.ts?raw';
import toolsHostedToolsExample from '../../../../../../examples/docs/tools/hostedTools.ts?raw';
import nonStrictSchemaTools from '../../../../../../examples/docs/tools/nonStrictSchemaTools.ts?raw';
import agentsAsToolsExample from '../../../../../../examples/docs/tools/agentsAsTools.ts?raw';
import mcpLocalServer from '../../../../../../examples/docs/tools/mcpLocalServer.ts?raw';

ツールはエージェントが **行動を取る** ための手段です。データ取得、外部 API 呼び出し、コード実行、さらにはコンピュータの使用まで行えます。JavaScript/TypeScript SDK は次の 4 つのカテゴリーをサポートします:

1. **組み込みツール（Hosted）** – OpenAI のサーバー上でモデルと並行して実行されます（Web 検索、ファイル検索、コンピュータ操作、Code Interpreter、画像生成）
2. **関数ツール** – 任意のローカル関数を JSON スキーマで包み、LLM が呼び出せるようにします
3. **ツールとしてのエージェント** – エージェント全体を呼び出し可能なツールとして公開します
4. **ローカル MCP サーバー** – マシン上で動作する Model Context Protocol サーバーを接続します

---

## 1. 組み込みツール（Hosted）

`OpenAIResponsesModel` を使うと、以下の組み込みツールを追加できます:

| Tool                    | Type string          | Purpose                                     |
| ----------------------- | -------------------- | ------------------------------------------- |
| Web search              | `'web_search'`       | インターネット検索                          |
| File / retrieval search | `'file_search'`      | OpenAI 上でホストされたベクトルストアの検索 |
| Computer use            | `'computer'`         | GUI 操作の自動化                            |
| Code Interpreter        | `'code_interpreter'` | サンドボックス環境でのコード実行            |
| Image generation        | `'image_generation'` | テキストに基づく画像生成                    |

<Code
  lang="typescript"
  code={toolsHostedToolsExample}
  title="組み込みツール（Hosted）"
/>

各パラメーターは OpenAI Responses API と一致します。`rankingOptions` やセマンティックフィルタのような高度なオプションは公式ドキュメントを参照してください。

---

## 2. 関数ツール

`tool()` ヘルパーで任意の関数をツールにできます。

<Code
  lang="typescript"
  code={toolsFunctionExample}
  title="Zod パラメーター付きの関数ツール"
/>

### オプションリファレンス

| Field           | Required | Description                                                                                                                    |
| --------------- | -------- | ------------------------------------------------------------------------------------------------------------------------------ |
| `name`          | No       | 既定は関数名（例: `get_weather`）                                                                                              |
| `description`   | Yes      | LLM に提示する、明確で人間が読める説明                                                                                         |
| `parameters`    | Yes      | Zod スキーマまたは元の JSON スキーマオブジェクトのいずれか。Zod のパラメーターを使うと自動で **strict** モードが有効になります |
| `strict`        | No       | `true`（デフォルト）の場合、引数が検証に通らないと SDK はモデルエラーを返します。あいまいな一致を許すには `false` に設定します |
| `execute`       | Yes      | `(args, context) => string \| Promise<string>` – ビジネスロジック。2 番目の引数は省略可能で、`RunContext` です                 |
| `errorFunction` | No       | 内部エラーをユーザー向けの文字列に変換するカスタムハンドラー `(context, error) => string`                                      |

### 厳密でない JSON スキーマのツール

不正または部分的な入力をモデルに推測させたい場合は、元の JSON スキーマ使用時に strict モードを無効化できます:

<Code
  lang="typescript"
  code={nonStrictSchemaTools}
  title="厳密でない JSON スキーマのツール"
/>

---

## 3. ツールとしてのエージェント

会話全体を完全にハンドオフせずに、あるエージェントが別のエージェントを支援させたい場合があります。`agent.asTool()` を使います:

<Code
  lang="typescript"
  code={agentsAsToolsExample}
  title="ツールとしてのエージェント"
/>

内部的に SDK は次を行います:

- 単一の `input` パラメーターを持つ関数ツールを作成
- ツール呼び出し時に、その入力でサブエージェントを実行
- 最後のメッセージ、または `customOutputExtractor` で抽出された出力を返却

---

## 4. ローカル MCP サーバー

ローカルの [Model Context Protocol](https://modelcontextprotocol.io/) サーバー経由でツールを公開し、エージェントに接続できます。`MCPServerStdio` を使ってサーバーを起動・接続します:

<Code lang="typescript" code={mcpLocalServer} title="ローカル MCP サーバー" />

完全な sample code は [`filesystem-example.ts`](https://github.com/openai/openai-agents-js/tree/main/examples/mcp/filesystem-example.ts) を参照してください。

---

## ツール使用の挙動

モデルがいつ・どのようにツールを使用すべきかの制御は、[エージェント](/openai-agents-js/ja/guides/agents#forcing-tool-use) を参照してください（`tool_choice`、`toolUseBehavior` など）。

---

## ベストプラクティス

- **短く明示的な説明** – ツールが何をするか、いつ使うかを記述する
- **入力を検証** – 可能な限り Zod スキーマで厳密な JSON 検証を行う
- **エラーハンドラーで副作用を避ける** – `errorFunction` は有用な文字列を返すだけにする（例外は投げない）
- **ツールは単一責任** – 小さく合成可能なツールはモデルの推論精度を高める

---

## 次のステップ

- [ツール使用の強制](/openai-agents-js/ja/guides/agents#forcing-tool-use) について学ぶ
- ツールの入力や出力を検証するために [ガードレール](/openai-agents-js/ja/guides/guardrails) を追加する
- [`tool()`](/openai-agents-js/openai/agents/functions/tool) と各種組み込みツール型の TypeDoc リファレンスを読む
