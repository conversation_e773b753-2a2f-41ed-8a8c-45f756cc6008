---
title: エージェントの実行結果
description: Learn how to access the results and output from your agent run
---

import { Code } from '@astrojs/starlight/components';
import handoffFinalOutputTypes from '../../../../../../examples/docs/results/handoffFinalOutputTypes.ts?raw';
import historyLoop from '../../../../../../examples/docs/results/historyLoop.ts?raw';

[エージェントの実行](/openai-agents-js/ja/guides/running-agents)を行うと、次のいずれかを受け取ります:

- [`RunResult`](/openai-agents-js/openai/agents/classes/runresult) — `stream: true` を付けずに `run` を呼び出した場合
- [`StreamedRunResult`](/openai-agents-js/openai/agents/classes/streamedrunresult) — `stream: true` を付けて `run` を呼び出した場合。ストリーミングの詳細は[ストリーミング](/openai-agents-js/ja/guides/streaming)も参照してください

## 最終出力

`finalOutput` プロパティには、最後に実行されたエージェントの最終出力が入ります。結果は次のいずれかです:

- `string` — `outputType` が定義されていないエージェントのデフォルト
- `unknown` — エージェントが出力タイプとして JSON スキーマを定義している場合。この場合 JSON はパースされていますが、型の検証は手動で行う必要があります
- `z.infer<outputType>` — エージェントが出力タイプとして Zod スキーマを定義している場合。出力は自動的にこのスキーマでパースされます
- `undefined` — エージェントが出力を生成しなかった場合（たとえば出力を生成する前に停止した場合）

異なる出力タイプを持つハンドオフを使用している場合は、エージェントの作成に `new Agent()` コンストラクタではなく `Agent.create()` メソッドを使用してください。

これにより、SDK が起こりうるすべてのハンドオフをまたいで出力タイプを推論し、`finalOutput` プロパティに対してユニオン型を提供できるようになります。

例:

<Code
  lang="typescript"
  code={handoffFinalOutputTypes}
  title="Handoff final output types"
/>

## 次ターンの入力

次ターン用の入力にアクセスする方法は 2 つあります:

- `result.history` — あなたの入力とエージェントの出力の両方のコピーが含まれます
- `result.output` — エージェント全体の実行の出力が含まれます

`history` は、チャットのようなユースケースで完全な履歴を保つのに便利です:

<Code lang="typescript" code={historyLoop} title="History loop" />

## 最後のエージェント

`lastAgent` プロパティには最後に実行されたエージェントが入ります。アプリケーションによっては、これは次回の ユーザー 入力時に有用です。たとえば、一次対応のトリアージ エージェントから言語別のエージェントへハンドオフする場合、最後のエージェントを保存しておき、次回 ユーザー がエージェントにメッセージを送る際に再利用できます。

ストリーミング モードでは、現在実行中のエージェントに対応する `currentAgent` プロパティへアクセスするのも有用です。

## 新規アイテム

`newItems` プロパティには、実行中に生成された新しいアイテムが入ります。アイテムは [`RunItem`](/openai-agents-js/openai/agents/type-aliases/runitem) です。ランアイテムは、LLM が生成した 元 のアイテムをラップします。これにより、LLM の出力に加えて、これらのイベントがどのエージェントに関連していたかにもアクセスできます。

- [`RunMessageOutputItem`](/openai-agents-js/openai/agents/classes/runmessageoutputitem) は LLM からのメッセージを示します。元のアイテムは生成されたメッセージです
- [`RunHandoffCallItem`](/openai-agents-js/openai/agents/classes/runhandoffcallitem) は LLM がハンドオフ ツールを呼び出したことを示します。元のアイテムは LLM からのツール呼び出しアイテムです
- [`RunHandoffOutputItem`](/openai-agents-js/openai/agents/classes/runhandoffoutputitem) はハンドオフが発生したことを示します。元のアイテムはハンドオフ ツール呼び出しに対するツールのレスポンスです。アイテムからソース/ターゲットのエージェントにもアクセスできます
- [`RunToolCallItem`](/openai-agents-js/openai/agents/classes/runtoolcallitem) は LLM がツールを起動したことを示します
- [`RunToolCallOutputItem`](/openai-agents-js/openai/agents/classes/runtoolcalloutputitem) はツールが呼び出されたことを示します。元のアイテムはツールのレスポンスです。アイテムからツールの出力にもアクセスできます
- [`RunReasoningItem`](/openai-agents-js/openai/agents/classes/runreasoningitem) は LLM からの推論アイテムを示します。元のアイテムは生成された推論です
- [`RunToolApprovalItem`](/openai-agents-js/openai/agents/classes/runtoolapprovalitem) は LLM がツール呼び出しの承認を要求したことを示します。元のアイテムは LLM からのツール呼び出しアイテムです

## 状態

`state` プロパティには実行の状態が入ります。`result` に付随する情報の多くは `state` から導出されますが、`state` はシリアライズ/デシリアライズ可能で、[エラーからの復旧](/openai-agents-js/ja/guides/running-agents#exceptions)や[`interruption`](#interruptions)への対応が必要な場合に、後続の `run` 呼び出しの入力としても使用できます。

## 中断

エージェントで `needsApproval` を使用している場合、続行前に処理が必要な `interruptions` が発生することがあります。この場合、`interruptions` は中断の原因となった `ToolApprovalItem` の配列になります。中断の扱い方の詳細は、[人間の介入（HITL）](/openai-agents-js/ja/guides/human-in-the-loop)ガイドを参照してください。

## その他の情報

### 元のレスポンス

`rawResponses` プロパティには、エージェントの実行中にモデルによって生成された 元 の LLM レスポンスが入ります。

### 最後のレスポンス ID

`lastResponseId` プロパティには、エージェントの実行中にモデルによって生成された最後のレスポンスの ID が入ります。

### ガードレールの結果

`inputGuardrailResults` と `outputGuardrailResults` プロパティには、存在する場合は ガードレール の結果が入ります。ガードレールの結果にはログや保存に有用な情報が含まれることがあるため、これらを利用できるようにしています。

### 元の入力

`input` プロパティには、run メソッドに渡した元の入力が入ります。ほとんどの場合これは不要ですが、必要な場合に備えて参照できます。
