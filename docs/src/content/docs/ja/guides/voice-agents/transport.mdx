---
title: リアルタイムトランスポート
description: Learn about the different transport layers that can be used with Realtime Agents.
---

import { Steps } from '@astrojs/starlight/components';
import { Code } from '@astrojs/starlight/components';

import createAgentExample from '../../../../../../../examples/docs/voice-agents/createAgent.ts?raw';
import multiAgentsExample from '../../../../../../../examples/docs/voice-agents/multiAgents.ts?raw';
import createSessionExample from '../../../../../../../examples/docs/voice-agents/createSession.ts?raw';
import configureSessionExample from '../../../../../../../examples/docs/voice-agents/configureSession.ts?raw';
import handleAudioExample from '../../../../../../../examples/docs/voice-agents/handleAudio.ts?raw';
import defineToolExample from '../../../../../../../examples/docs/voice-agents/defineTool.ts?raw';
import toolApprovalEventExample from '../../../../../../../examples/docs/voice-agents/toolApprovalEvent.ts?raw';
import guardrailsExample from '../../../../../../../examples/docs/voice-agents/guardrails.ts?raw';
import guardrailSettingsExample from '../../../../../../../examples/docs/voice-agents/guardrailSettings.ts?raw';
import audioInterruptedExample from '../../../../../../../examples/docs/voice-agents/audioInterrupted.ts?raw';
import sessionInterruptExample from '../../../../../../../examples/docs/voice-agents/sessionInterrupt.ts?raw';
import sessionHistoryExample from '../../../../../../../examples/docs/voice-agents/sessionHistory.ts?raw';
import historyUpdatedExample from '../../../../../../../examples/docs/voice-agents/historyUpdated.ts?raw';
import updateHistoryExample from '../../../../../../../examples/docs/voice-agents/updateHistory.ts?raw';
import customWebRTCTransportExample from '../../../../../../../examples/docs/voice-agents/customWebRTCTransport.ts?raw';
import websocketSessionExample from '../../../../../../../examples/docs/voice-agents/websocketSession.ts?raw';
import transportEventsExample from '../../../../../../../examples/docs/voice-agents/transportEvents.ts?raw';
import thinClientExample from '../../../../../../../examples/docs/voice-agents/thinClient.ts?raw';

## 既定のトランスポート層

### WebRTC 接続

既定のトランスポート層は WebRTC を使用します。音声はマイクから録音され、自動的に再生されます。

独自のメディアストリームまたはオーディオ要素を使用するには、セッション作成時に `OpenAIRealtimeWebRTC` インスタンスを指定します。

<Code lang="typescript" code={customWebRTCTransportExample} />

### WebSocket 接続

WebRTC の代わりに WebSocket 接続を使用するには、セッション作成時に `transport: 'websocket'` または `OpenAIRealtimeWebSocket` のインスタンスを渡します。これはサーバー側のユースケースに適しており、たとえば Twilio で電話エージェントを構築する場合などです。

<Code lang="typescript" code={websocketSessionExample} />

録音/再生ライブラリは任意のものを使用して、元の PCM16 オーディオバイトを処理できます。

### 独自のトランスポート機構の構築

別の音声対音声 API を使用したい場合や、独自のカスタムトランスポート機構を持ちたい場合は、`RealtimeTransportLayer` インターフェースを実装し、`RealtimeTransportEventTypes` イベントを発火することで作成できます。

## Realtime API とのより直接的なやり取り

OpenAI Realtime API を使用しつつ、Realtime API へのアクセスをより直接的に行いたい場合は、次の 2 つの方法があります。

### オプション 1 - トランスポート層へのアクセス

`RealtimeSession` のあらゆる機能の恩恵を受けつつ、`session.transport` を通じてトランスポート層にアクセスできます。

トランスポート層は受信したすべてのイベントを `*` イベントで発火し、`sendEvent()` メソッドで元のイベントを送信できます。

<Code lang="typescript" code={transportEventsExample} />

### オプション 2 — トランスポート層のみの使用

ツールの自動実行やガードレールなどが不要な場合は、接続と割り込みのみを管理する「薄い」クライアントとして、トランスポート層だけを使用できます。

<Code lang="typescript" code={thinClientExample} />
