---
title: Realtime Agent を Twilio に接続
description: Connect your Agents SDK agents to Twilio to use voice agents
---

import { Aside, Steps, Code } from '@astrojs/starlight/components';
import twilioBasicExample from '../../../../../../examples/docs/extensions/twilio-basic.ts?raw';
import twilioServerExample from '../../../../../../examples/realtime-twilio/index.ts?raw';

Twilio は、通話の 元 audio を WebSocket サーバーへ送信する [Media Streams API](https://www.twilio.com/docs/voice/media-streams) を提供しています。このセットアップを使うと、[音声エージェントの概要](/openai-agents-js/ja/guides/voice-agents) を Twilio に接続できます。Twilio から届くイベントを Realtime Session に接続するには、`websocket` モードのデフォルト Realtime Session トランスポートを使用できます。ただし、通話は Web ベースの会話よりも遅延が大きくなるため、適切な audio フォーマットの設定と、独自の割り込みタイミングの調整が必要です。

セットアップの体験を向上させるため、Twi<PERSON> への接続、割り込み処理、audio 転送を含めて処理する専用のトランスポートレイヤーを用意しました。

<Aside type="caution">
  このアダプターはまだベータ版です。レアケースの問題やバグに遭遇する可能性があります。
  問題があれば [GitHub
  issues](https://github.com/openai/openai-agents-js/issues)
  から報告してください。迅速に対応します。
</Aside>

## セットアップ

<Steps>

1. **Twilio アカウントと Twilio の電話番号を用意します。**

2. **Twilio からのイベントを受け取れる WebSocket サーバーをセットアップします。**

   ローカル開発の場合、[`ngrok`](https://ngrok.io/) や
   [Cloudflare Tunnel](https://developers.cloudflare.com/pages/how-to/preview-with-cloudflare-tunnel/)
   のようなローカルトンネルの設定が必要になり、ローカルサーバーを Twilio から到達可能にします。`TwilioRealtimeTransportLayer`
   を使って Twilio に接続できます。

3. **extensions パッケージをインストールして Twilio アダプターを導入します:**

   ```bash
   npm install @openai/agents-extensions
   ```

4. **アダプターと model をインポートして `RealtimeSession` に接続します:**

   <Code
     lang="typescript"
     code={twilioBasicExample.replace(
       /\n\s+\/\/ @ts-expect-error - this is not defined/g,
       '',
     )}
   />

5. **`RealtimeSession` を Twilio に接続します:**

   ```typescript
   session.connect({ apiKey: 'your-openai-api-key' });
   ```

</Steps>

`RealtimeSession` に期待されるあらゆるイベントや挙動は、ツール呼び出し、ガードレールなどを含めて、そのまま機能します。`RealtimeSession` を音声エージェントで使う方法については、[音声エージェントの概要](/openai-agents-js/ja/guides/voice-agents) を参照してください。

## ヒントと考慮事項

1. **スピードが最重要です。**

   Twilio から必要なイベントと audio をすべて受け取るため、WebSocket 接続の参照を取得したらすぐに
   `TwilioRealtimeTransportLayer` インスタンスを作成し、直ちに `session.connect()` を呼び出してください。

2. **Twilio の 元 イベントにアクセスします。**

   Twilio から送信される 元 イベントにアクセスしたい場合は、`RealtimeSession` インスタンスの `transport_event`
   をリッスンします。Twilio からのすべてのイベントは `twilio_message` という type と、元イベントデータを含む `message` プロパティを持ちます。

3. **デバッグログを確認します。**

   事象の詳細が必要になる場合があります。環境変数 `DEBUG=openai-agents*` を使うと Agents SDK のすべてのデバッグログが表示されます。
   もしくは、Twilio アダプターのデバッグログのみを有効化するには `DEBUG=openai-agents:extensions:twilio*` を使用します。

## フルサンプルサーバー

以下は、Twilio からのリクエストを受け取り、それを `RealtimeSession` に転送する WebSocket サーバーのエンドツーエンドのサンプルです。

<Code
  lang="typescript"
  code={twilioServerExample}
  title="Fastify を使用したサーバー例"
/>
