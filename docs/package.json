{"private": true, "name": "docs", "type": "module", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "translate": "tsx src/scripts/translate.ts"}, "dependencies": {"@astrojs/starlight": "^0.35.2", "@astrojs/starlight-tailwind": "^4.0.1", "@openai/agents": "workspace:*", "@tailwindcss/vite": "^4.0.17", "astro": "^5.13.5", "sharp": "^0.34.2", "starlight-llms-txt": "^0.6.0", "starlight-typedoc": "^0.21.3", "typedoc": "^0.28.1", "typedoc-plugin-markdown": "^4.8.1"}, "devDependencies": {"tailwindcss": "^3.3.3", "tsx": "^4.19.4", "typedoc-plugin-frontmatter": "^1.3.0", "typedoc-plugin-zod": "^1.4.1"}}