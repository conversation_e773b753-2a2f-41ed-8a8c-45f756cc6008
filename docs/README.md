# Docs

The documentation is generated using Astro Starlight.

## Running the docs

To run the docs from the root of the project run:

```bash
pnpm docs:dev
```

## Translating docs

All of our documentation is available in Japanese. For this we use a script to translate the docs.

```bash
pnpm docs:translate
```

## Building the docs

The docs are automatically built and deployed using GitHub Actions. To build them locally run:

```bash
pnpm docs:build
```
