{"name": "@openai/agents-openai", "repository": "https://github.com/openai/openai-agents-js", "homepage": "https://openai.github.io/openai-agents-js/", "version": "0.1.0", "description": "The OpenAI Agents SDK is a lightweight yet powerful framework for building multi-agent workflows.", "author": "OpenAI <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}}, "dependencies": {"@openai/agents-core": "workspace:*", "debug": "^4.4.0", "openai": "^5.16.0"}, "scripts": {"prebuild": "tsx ../../scripts/embedMeta.ts", "build": "tsc", "build-check": "tsc --noEmit -p ./tsconfig.test.json"}, "keywords": ["openai", "agents", "ai", "agentic"], "license": "MIT", "peerDependencies": {"zod": "^3.25.40"}, "devDependencies": {"@ai-sdk/provider": "^1.1.3", "@types/debug": "^4.1.12", "zod": "^3.25.40"}, "files": ["dist"]}