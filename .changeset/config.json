{"$schema": "https://unpkg.com/@changesets/config@3.1.1/schema.json", "changelog": "@changesets/cli/changelog", "commit": false, "fixed": [], "linked": [["@openai/agents", "@openai/agents-core", "@openai/agents-openai", "@openai/agents-realtime", "@openai/agents-extensions"]], "access": "restricted", "baseBranch": "main", "updateInternalDependencies": "patch", "___experimentalUnsafeOptions_WILL_CHANGE_IN_PATCH": {"onlyUpdatePeerDependentsWhenOutOfRange": true}, "ignore": []}