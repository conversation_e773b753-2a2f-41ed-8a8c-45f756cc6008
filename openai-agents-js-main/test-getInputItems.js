/**
 * Unit test for the getInputItems function fix
 * This tests the core logic without needing to build the entire project
 */

// Mock the getInputItems function logic
function isMessageItem(item) {
  return item && typeof item === 'object' && 'role' in item;
}

function getInputItems(input, conversationId) {
  if (typeof input === 'string') {
    return [{ role: 'user', content: input }];
  }

  // When using conversationId, the OpenAI Responses API automatically retrieves
  // the conversation history. To avoid duplicate items with the same IDs,
  // we need to filter out items that would already be present in the conversation.
  // We keep only the items from the current turn (typically the last few items).
  let filteredInput = input;
  if (conversationId) {
    // Find the last user message to identify the start of the current turn
    let lastUserMessageIndex = -1;
    for (let i = input.length - 1; i >= 0; i--) {
      const item = input[i];
      if (isMessageItem(item) && item.role === 'user') {
        lastUserMessageIndex = i;
        break;
      }
    }
    
    // If we found a user message, only include items from that point onwards
    // This represents the current turn's conversation
    if (lastUserMessageIndex >= 0) {
      filteredInput = input.slice(lastUserMessageIndex);
    } else {
      // If no user message found, include all items (fallback)
      filteredInput = input;
    }
  }

  // For this test, just return the filtered input
  return filteredInput;
}

// Test cases
function runTests() {
  console.log('🧪 Testing getInputItems function logic...\n');

  // Test case 1: Without conversationId, all items should be included
  const items1 = [
    { role: 'user', content: 'first message', id: 'u1' },
    { role: 'assistant', content: 'response 1', id: 'a1' },
    { role: 'user', content: 'second message', id: 'u2' },
    { type: 'function_call', id: 'f1', name: 'test_tool' },
    { type: 'function_call_result', id: 'fr1', output: 'tool result' },
  ];

  const result1 = getInputItems(items1);
  console.log('Test 1 - Without conversationId:');
  console.log(`Input length: ${items1.length}, Output length: ${result1.length}`);
  console.log(`✅ ${result1.length === 5 ? 'PASS' : 'FAIL'} - All items included\n`);

  // Test case 2: With conversationId, only items from last user message onwards
  const result2 = getInputItems(items1, 'conv_123');
  console.log('Test 2 - With conversationId:');
  console.log(`Input length: ${items1.length}, Output length: ${result2.length}`);
  console.log(`Expected: 3 items (user message + tool call + tool result)`);
  console.log(`✅ ${result2.length === 3 ? 'PASS' : 'FAIL'} - Only current turn items included`);
  console.log(`First item role: ${result2[0].role} (should be 'user')`);
  console.log(`✅ ${result2[0].role === 'user' ? 'PASS' : 'FAIL'} - Starts with user message\n`);

  // Test case 3: Edge case - no user message
  const items3 = [
    { type: 'function_call', id: 'f1', name: 'test_tool' },
    { type: 'function_call_result', id: 'fr1', output: 'tool result' },
  ];

  const result3 = getInputItems(items3, 'conv_456');
  console.log('Test 3 - With conversationId but no user message:');
  console.log(`Input length: ${items3.length}, Output length: ${result3.length}`);
  console.log(`✅ ${result3.length === 2 ? 'PASS' : 'FAIL'} - Fallback to all items\n`);

  console.log('🎉 All tests completed!');
  console.log('The fix logic appears to be working correctly.');
}

runTests();
