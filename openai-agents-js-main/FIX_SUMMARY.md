# Fix for Issue #425: Agent + tool + conversationId throws duplicate ID error

## Problem Description

When using the OpenAI Agents SDK with:
- An agent that has tools
- A `conversationId` parameter
- Tool calls that require multiple API interactions

The system would throw an error like:
```
BadRequestError: 400 Duplicate item found with id rs_68b8366ad2848195beb3348e5860cc81029faaa0843e7ce0. Remove duplicate items from your input and try again.
```

## Root Cause Analysis

The issue occurred because:

1. **OpenAI Responses API Behavior**: When a `conversationId` is provided, the OpenAI Responses API automatically retrieves and includes the conversation history in the context.

2. **SDK Behavior**: The SDK was also sending the full conversation history as part of the `input` parameter through the `getTurnInput` function.

3. **Duplicate Items**: This resulted in the same items (with the same IDs) being present twice:
   - Once from the conversation history retrieved by the API
   - Once from the input sent by the SDK

4. **When It Happens**: The error typically occurs on the second API call after a tool execution, when the SDK tries to send the conversation history that includes the tool call and its result.

## Solution

The fix modifies the `getInputItems` function in `packages/agents-openai/src/openaiResponsesModel.ts` to filter the input when a `conversationId` is provided.

### Key Changes

1. **Modified `getInputItems` function signature**:
   ```typescript
   function getInputItems(
     input: ModelRequest['input'],
     conversationId?: string,  // Added conversationId parameter
   ): OpenAI.Responses.ResponseInputItem[]
   ```

2. **Added filtering logic**:
   - When `conversationId` is provided, the function identifies the current turn by finding the last user message
   - It then only includes items from that point onwards (current turn)
   - This prevents sending duplicate items that would already be in the conversation history

3. **Updated the call site**:
   ```typescript
   const input = getInputItems(request.input, request.conversationId);
   ```

### Filtering Logic

```typescript
if (conversationId) {
  // Find the last user message to identify the start of the current turn
  let lastUserMessageIndex = -1;
  for (let i = input.length - 1; i >= 0; i--) {
    const item = input[i];
    if (isMessageItem(item) && item.role === 'user') {
      lastUserMessageIndex = i;
      break;
    }
  }
  
  // If we found a user message, only include items from that point onwards
  if (lastUserMessageIndex >= 0) {
    filteredInput = input.slice(lastUserMessageIndex);
  } else {
    // If no user message found, include all items (fallback)
    filteredInput = input;
  }
}
```

## Testing

Added a test case in `packages/agents-openai/test/openaiResponsesModel.helpers.test.ts` to verify:
- Without `conversationId`: all items are included (existing behavior)
- With `conversationId`: only items from the current turn are included (new behavior)

## Impact

- **Fixes the duplicate ID error** when using `conversationId` with tools
- **Maintains backward compatibility** for cases without `conversationId`
- **Reduces API payload size** when using `conversationId` by avoiding duplicate data
- **No breaking changes** to the public API

## Files Modified

1. `packages/agents-openai/src/openaiResponsesModel.ts`
   - Modified `getInputItems` function to accept and handle `conversationId`
   - Added filtering logic to prevent duplicate items

2. `packages/agents-openai/test/openaiResponsesModel.helpers.test.ts`
   - Added test case to verify the filtering behavior

## Example Usage

The fix allows this code to work without errors:

```javascript
import { Agent, run, tool } from '@openai/agents';
import { z } from 'zod';
import OpenAI from 'openai';

const getWeatherTool = tool({
    name: 'get_weather',
    description: 'Get the weather for a given city',
    parameters: z.object({ city: z.string() }),
    strict: true,
    async execute({ city }) {
        return `The weather in ${city} is sunny.`;
    },
});

async function main() {
    const client = new OpenAI();
    const conversation = await client.conversations.create({});
    const agent = new Agent({
        name: 'Assistant',
        instructions: 'You are a helpful assistant. be VERY concise.',
        model: "gpt-4o",
        tools: [getWeatherTool],
    });

    const runOptions = { conversationId: conversation.id };
    const result = await run(agent, "What is the weather in NYC?", runOptions);
    console.log(`Output: ${result.finalOutput}`);
}
```

This code would previously fail with the duplicate ID error but now works correctly.
