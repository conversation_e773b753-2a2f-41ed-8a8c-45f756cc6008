{"name": "realtime-next", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "build-check": "tsc --noEmit"}, "dependencies": {"@openai/agents": "workspace:*", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "15.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "wavtools": "^0.1.5", "zod": "^3.25.40"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}