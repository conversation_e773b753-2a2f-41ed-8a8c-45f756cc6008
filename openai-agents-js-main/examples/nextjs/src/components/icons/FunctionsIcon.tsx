import * as React from 'react';

const FunctionsIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    {...props}
    viewBox="0 0 24 24"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 3a4 4 0 0 0-4 4c0 .277.005.55.01.805l.001.038c.005.271.01.523.009.764-.003.488-.031.88-.108 1.207-.074.314-.186.54-.346.718-.16.177-.418.364-.875.517A.994.994 0 0 0 1 12a.998.998 0 0 0 .692.951c.438.147.69.328.847.503.159.176.273.402.35.717.08.327.114.722.122 1.211.005.297.001.577-.003.88C3.003 16.49 3 16.73 3 17a4 4 0 0 0 4 4 1 1 0 1 0 0-2 2 2 0 0 1-2-2c0-.204.003-.426.006-.653.004-.34.009-.69.004-.997-.009-.541-.046-1.111-.179-1.655-.136-.554-.378-1.104-.809-1.581a3.285 3.285 0 0 0-.1-.107c.044-.045.088-.09.13-.137.436-.485.676-1.04.807-1.6.128-.546.157-1.116.16-1.651a32.24 32.24 0 0 0-.008-.815v-.032C5.004 7.512 5 7.257 5 7a2 2 0 0 1 2-2 1 1 0 0 0 0-2Zm13.06 9c-.04.04-.08.08-.117.123-.44.482-.681 1.036-.811 1.594-.127.545-.154 1.115-.155 1.65 0 .269.005.544.011.812v.007c.006.273.012.542.012.814a2 2 0 0 1-2 2 1 1 0 1 0 0 2 4 4 0 0 0 4-4c0-.296-.006-.584-.012-.854v-.004c-.006-.274-.011-.527-.01-.77 0-.491.027-.88.101-1.2.072-.308.183-.528.341-.702.16-.174.421-.362.889-.519A.994.994 0 0 0 23 12a1 1 0 0 0-.692-.951c-.468-.157-.73-.345-.889-.52-.159-.173-.269-.393-.34-.7-.075-.321-.102-.71-.103-1.201 0-.243.005-.496.01-.77l.001-.004c.006-.27.012-.558.012-.854a4 4 0 0 0-4-4 1 1 0 1 0 0 2 2 2 0 0 1 2 2c0 .272-.006.54-.012.815v.006c-.006.268-.011.543-.01.811 0 .536.027 1.106.154 1.65.13.56.37 1.113.81 1.595.039.042.078.083.118.123Zm-5.084-5.217a1 1 0 0 1-.76 1.193c-.335.075-.534.22-.68.415-.166.218-.304.55-.397 1.042-.035.18-.062.37-.082.567h.443a1 1 0 1 1 0 2h-.507l.003.418c.002.27.004.547.004.832 0 1.466-.261 2.656-.882 3.5-.665.902-1.622 1.25-2.618 1.25a1 1 0 1 1 0-2c.504 0 .797-.152 1.007-.437.254-.344.493-1.029.493-2.313 0-.237-.002-.481-.004-.73l-.004-.52H10.5a1 1 0 1 1 0-2h.55c.027-.327.067-.644.124-.943.125-.653.346-1.318.767-1.873.44-.58 1.053-.985 1.842-1.16a1 1 0 0 1 1.193.759Z"
    />
  </svg>
);

export default FunctionsIcon;
