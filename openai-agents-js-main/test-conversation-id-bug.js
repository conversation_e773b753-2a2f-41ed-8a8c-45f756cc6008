/**
 * Test case to reproduce and verify the fix for the conversationId + tools duplicate ID bug
 * Issue: https://github.com/openai/openai-agents-js/issues/425
 */

import { Agent, run, tool } from '@openai/agents';
import { z } from 'zod';
import OpenAI from 'openai';

const getWeatherTool = tool({
    name: 'get_weather',
    description: 'Get the weather for a given city',
    parameters: z.object({ city: z.string() }),
    strict: true,
    async execute({ city }) {
        console.log(`Getting weather for ${city}...`);
        return `The weather in ${city} is sunny.`;
    },
});

async function testConversationIdWithTools() {
    console.log('Testing conversationId with tools...');
    
    try {
        const client = new OpenAI();
        const conversation = await client.conversations.create({});
        console.log(`Created conversation: ${conversation.id}`);
        
        const agent = new Agent({
            name: 'Assistant',
            instructions: 'You are a helpful assistant. be VERY concise.',
            model: "gpt-4o",
            tools: [getWeatherTool],
        });

        const runOptions = { conversationId: conversation.id };
        console.log('Running agent with conversationId and tools...');
        
        const result = await run(agent, "What is the weather in NYC?", runOptions);
        console.log(`Success! Output: ${result.finalOutput}`);
        
        return true;
    } catch (error) {
        console.error('Test failed with error:', error.message);
        if (error.message.includes('Duplicate item found with id rs_')) {
            console.error('❌ The duplicate ID bug is still present!');
            return false;
        } else {
            console.error('❌ Test failed with a different error');
            return false;
        }
    }
}

async function main() {
    console.log('🧪 Testing fix for conversationId + tools duplicate ID bug');
    console.log('=' .repeat(60));
    
    const success = await testConversationIdWithTools();
    
    if (success) {
        console.log('✅ Test passed! The duplicate ID bug appears to be fixed.');
    } else {
        console.log('❌ Test failed! The bug may still be present.');
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}
