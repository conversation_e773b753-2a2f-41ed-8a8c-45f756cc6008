#!/bin/bash

# Test script for the conversationId + tools duplicate ID fix
# Make sure you have OPENAI_API_KEY set in your environment

echo "🧪 Testing fix for conversationId + tools duplicate ID bug"
echo "=" | head -c 60 && echo

# Check if OPENAI_API_KEY is set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "❌ OPENAI_API_KEY environment variable is not set"
    echo "Please set your OpenAI API key:"
    echo "export OPENAI_API_KEY=your_key_here"
    exit 1
fi

echo "✅ OPENAI_API_KEY is set"

# Build the project
echo "🔨 Building the project..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"

# Run the test
echo "🚀 Running the test case..."
node test-conversation-id-bug.js

if [ $? -eq 0 ]; then
    echo "✅ Test passed! The fix appears to be working."
else
    echo "❌ Test failed. The bug may still be present."
    exit 1
fi
