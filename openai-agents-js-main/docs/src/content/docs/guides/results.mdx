---
title: Results
description: Learn how to access the results and output from your agent run
---

import { Code } from '@astrojs/starlight/components';
import handoffFinalOutputTypes from '../../../../../examples/docs/results/handoffFinalOutputTypes.ts?raw';
import historyLoop from '../../../../../examples/docs/results/historyLoop.ts?raw';

When you [run your agent](/openai-agents-js/guides/running-agents), you will either receive a:

- [`RunResult`](/openai-agents-js/openai/agents/classes/runresult) if you call `run` without `stream: true`
- [`StreamedRunResult`](/openai-agents-js/openai/agents/classes/streamedrunresult) if you call `run` with `stream: true`. For details on streaming, also check the [streaming guide](/openai-agents-js/guides/streaming).

## Final output

The `finalOutput` property contains the final output of the last agent that ran. This result is either:

- `string` — default for any agent that has no `outputType` defined
- `unknown` — if the agent has a JSON schema defined as output type. In this case the JSON was parsed but you still have to verify its type manually.
- `z.infer<outputType>` — if the agent has a Zod schema defined as output type. The output will automatically be parsed against this schema.
- `undefined` — if the agent did not produce an output (for example stopped before it could produce an output)

If you are using handoffs with different output types, you should use the `Agent.create()` method instead of the `new Agent()` constructor to create your agents.

This will enable the SDK to infer the output types across all possible handoffs and provide a union type for the `finalOutput` property.

For example:

<Code
  lang="typescript"
  code={handoffFinalOutputTypes}
  title="Handoff final output types"
/>

## Inputs for the next turn

There are two ways you can access the inputs for your next turn:

- `result.history` — contains a copy of both your input and the output of the agents.
- `result.output` — contains the output of the full agent run.

`history` is a convenient way to maintain a full history in a chat-like use case:

<Code lang="typescript" code={historyLoop} title="History loop" />

## Last agent

The `lastAgent` property contains the last agent that ran. Depending on your application, this is often useful for the next time the user inputs something. For example, if you have a frontline triage agent that hands off to a language-specific agent, you can store the last agent, and reuse it the next time the user messages the agent.

In streaming mode it can also be useful to access the `currentAgent` property that's mapping to the current agent that is running.

## New items

The `newItems` property contains the new items generated during the run. The items are [`RunItem`](/openai-agents-js/openai/agents/type-aliases/runitem)s. A run item wraps the raw item generated by the LLM. These can be used to access additionally to the output of the LLM which agent these events were associated with.

- [`RunMessageOutputItem`](/openai-agents-js/openai/agents/classes/runmessageoutputitem) indicates a message from the LLM. The raw item is the message generated.
- [`RunHandoffCallItem`](/openai-agents-js/openai/agents/classes/runhandoffcallitem) indicates that the LLM called the handoff tool. The raw item is the tool call item from the LLM.
- [`RunHandoffOutputItem`](/openai-agents-js/openai/agents/classes/runhandoffoutputitem) indicates that a handoff occurred. The raw item is the tool response to the handoff tool call. You can also access the source/target agents from the item.
- [`RunToolCallItem`](/openai-agents-js/openai/agents/classes/runtoolcallitem) indicates that the LLM invoked a tool.
- [`RunToolCallOutputItem`](/openai-agents-js/openai/agents/classes/runtoolcalloutputitem) indicates that a tool was called. The raw item is the tool response. You can also access the tool output from the item.
- [`RunReasoningItem`](/openai-agents-js/openai/agents/classes/runreasoningitem) indicates a reasoning item from the LLM. The raw item is the reasoning generated.
- [`RunToolApprovalItem`](/openai-agents-js/openai/agents/classes/runtoolapprovalitem) indicates that the LLM requested approval for a tool call. The raw item is the tool call item from the LLM.

## State

The `state` property contains the state of the run. Most of what is attached to the `result` is derived from the `state` but the `state` is serializable/deserializable and can also be used as input for a subsequent call to `run` in case you need to [recover from an error](/openai-agents-js/guides/running-agents#exceptions) or deal with an [`interruption`](#interruptions).

## Interruptions

If you are using `needsApproval` in your agent, your `run` might trigger some `interruptions` that you need to handle before continuing. In that case `interruptions` will be an array of `ToolApprovalItem`s that caused the interruption. Check out the [human-in-the-loop guide](/openai-agents-js/guides/human-in-the-loop) for more information on how to work with interruptions.

## Other information

### Raw responses

The `rawResponses` property contains the raw LLM responses generated by the model during the agent run.

### Last response ID

The `lastResponseId` property contains the ID of the last response generated by the model during the agent run.

### Guardrail results

The `inputGuardrailResults` and `outputGuardrailResults` properties contain the results of the guardrails, if any. Guardrail results can sometimes contain useful information you want to log or store, so we make these available to you.

### Original input

The `input` property contains the original input you provided to the run method. In most cases you won't need this, but it's available in case you do.
