---
title: Guardrails
description: Validate or transform agent input and output
---

import { Code } from '@astrojs/starlight/components';
import inputGuardrailExample from '../../../../../examples/docs/guardrails/guardrails-input.ts?raw';
import outputGuardrailExample from '../../../../../examples/docs/guardrails/guardrails-output.ts?raw';

Guardrails run _in parallel_ to your agents, allowing you to perform checks and validations on user input or agent output. For example, you may run a lightweight model as a guardrail before invoking an expensive model. If the guardrail detects malicious usage, it can trigger an error and stop the costly model from running.

There are two kinds of guardrails:

1. **Input guardrails** run on the initial user input.
2. **Output guardrails** run on the final agent output.

## Input guardrails

Input guardrails run in three steps:

1. The guardrail receives the same input passed to the agent.
2. The guardrail function executes and returns a [`GuardrailFunctionOutput`](/openai-agents-js/openai/agents/interfaces/guardrailfunctionoutput) wrapped inside an [`InputGuardrailResult`](/openai-agents-js/openai/agents/interfaces/inputguardrailresult).
3. If `tripwireTriggered` is `true`, an [`InputGuardrailTripwireTriggered`](/openai-agents-js/openai/agents/classes/inputguardrailtripwiretriggered) error is thrown.

> **Note**
> Input guardrails are intended for user input, so they only run if the agent is the _first_ agent in the workflow. Guardrails are configured on the agent itself because different agents often require different guardrails.

## Output guardrails

Output guardrails follow the same pattern:

1. The guardrail receives the same input passed to the agent.
2. The guardrail function executes and returns a [`GuardrailFunctionOutput`](/openai-agents-js/openai/agents/interfaces/guardrailfunctionoutput) wrapped inside an [`OutputGuardrailResult`](/openai-agents-js/openai/agents/interfaces/outputguardrailresult).
3. If `tripwireTriggered` is `true`, an [`OutputGuardrailTripwireTriggered`](/openai-agents-js/openai/agents/classes/outputguardrailtripwiretriggered) error is thrown.

> **Note**
> Output guardrails only run if the agent is the _last_ agent in the workflow. For realtime voice interactions see [the voice agents guide](/openai-agents-js/guides/voice-agents/build#guardrails).

## Tripwires

When a guardrail fails, it signals this via a tripwire. As soon as a tripwire is triggered, the runner throws the corresponding error and halts execution.

## Implementing a guardrail

A guardrail is simply a function that returns a `GuardrailFunctionOutput`. Below is a minimal example that checks whether the user is asking for math homework help by running another agent under the hood.

<Code
  lang="typescript"
  code={inputGuardrailExample}
  title="Input guardrail example"
/>

Output guardrails work the same way.

<Code
  lang="typescript"
  code={outputGuardrailExample}
  title="Output guardrail example"
/>

1. `guardrailAgent` is used inside the guardrail functions.
2. The guardrail function receives the agent input or output and returns the result.
3. Extra information can be included in the guardrail result.
4. `agent` defines the actual workflow where guardrails are applied.
