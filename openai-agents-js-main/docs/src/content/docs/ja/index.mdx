---
title: OpenAI Agents SDK TypeScript
description: The OpenAI Agents SDK for TypeScript enables you to build agentic AI apps in a lightweight, easy-to-use package with very few abstractions.
---

import { LinkCard } from '@astrojs/starlight/components';
import { Code } from '@astrojs/starlight/components';
import Hero from '../../../components/Hero.astro';

import helloWorldExample from '../../../../../examples/docs/hello-world.ts?raw';

<Hero>
  <Fragment slot="title">クイックスタート</Fragment>
  <Fragment slot="description">
    ほんの数分ではじめてのエージェントをつくることができます。
  </Fragment>
  <Fragment slot="cta">はじめる</Fragment>
</Hero>

## 概要

[OpenAI Agents SDK for TypeScript](https://github.com/openai/openai-agents-js) は、軽量で使いやすく、抽象化の少ないパッケージで エージェント型 AI アプリを構築できるようにします。これは、以前のエージェント向け実験である [Swarm](https://github.com/openai/swarm/tree/main) の本番運用向けアップグレードで、[Python 版も利用可能](https://github.com/openai/openai-agents-python)です。Agents SDK は非常に少数の基本コンポーネントで構成されています:

- **エージェント**: instructions と tools を備えた LLM
- **ハンドオフ**: 特定のタスクに対して、エージェントが他のエージェントに委譲できる機能
- **ガードレール**: エージェントへの入力を検証できる機能

これらの基本コンポーネントは、 TypeScript と組み合わせることで、ツールとエージェント間の複雑な関係を表現し、急な学習コストなしに実アプリケーションを構築できます。さらに、 SDK には組み込みの **トレーシング** があり、エージェントのフローを可視化・デバッグし、評価や、アプリケーション向けモデルのファインチューニングまで行えます。

## Agents SDK を使う理由

SDK には次の 2 つの設計原則があります:

1. 使う価値がある十分な機能を備えつつ、学習を速くするために基本コンポーネントは最小限
2. そのままでもうまく動作しつつ、挙動を細かくカスタマイズ可能

SDK の主な機能は次のとおりです:

- **エージェント ループ**: ツールの呼び出し、結果の LLM への送信、 LLM が完了するまでのループを内蔵で処理
- **TypeScript ファースト**: 新しい抽象を学ばずに、言語機能でエージェントのオーケストレーションや連鎖を実現
- **ハンドオフ**: 複数エージェント間の調整と委譲を可能にする強力な機能
- **ガードレール**: エージェントと並行して入力の検証やチェックを実行し、失敗時は早期に中断
- **関数ツール**: 任意の TypeScript 関数をツール化し、自動スキーマ生成と Zod ベースの検証を提供
- **トレーシング**: ワークフローの可視化・デバッグ・監視に加え、 OpenAI の評価、ファインチューニング、蒸留ツール群を活用可能
- **リアルタイムエージェント**: 自動割り込み検知、コンテキスト管理、ガードレールなどを備えた強力な音声エージェントを構築

## インストール

```bash
npm install @openai/agents zod@3
```

## Hello World の例

<Code lang="typescript" code={helloWorldExample} title="Hello World" />

(_これを実行する場合は、`OPENAI_API_KEY` 環境変数を設定してください_)

```bash
export OPENAI_API_KEY=sk-...
```
