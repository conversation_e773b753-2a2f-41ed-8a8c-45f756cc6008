---
title: AI SDK で任意モデルを指定
description: Connect your Agents SDK agents to any model through the Vercel's AI SDK
---

import { Aside, Steps, Code } from '@astrojs/starlight/components';
import aiSdkSetupExample from '../../../../../../examples/docs/extensions/ai-sdk-setup.ts?raw';

<Aside type="caution">
  このアダプターはまだベータ版です。特に小規模なモデルプロバイダーでは問題が発生する可能性があります。問題が発生した場合は
  [GitHub issues](https://github.com/openai/openai-agents-js/issues)
  からご報告ください。迅速に修正します。
</Aside>

Agents SDK は標準で Responses API または Chat Completions API を通じて OpenAI モデルと連携します。別のモデルを使用したい場合は、[Vercel の AI SDK](https://sdk.vercel.ai/) が提供する多様な対応モデルを、このアダプター経由で Agents SDK に組み込めます。

## セットアップ

<Steps>

1. extensions パッケージをインストールして AI SDK アダプターを導入します:

   ```bash
   npm install @openai/agents-extensions
   ```

2. [Vercel の AI SDK](https://ai-sdk.dev/docs/foundations/providers-and-models) から目的のモデルパッケージを選び、インストールします:

   ```bash
   npm install @ai-sdk/openai
   ```

3. アダプターとモデルをインポートして、エージェントに接続します:

   ```typescript
   import { openai } from '@ai-sdk/openai';
   import { aisdk } from '@openai/agents-extensions';
   ```

4. エージェントで使用するモデルのインスタンスを初期化します:

   ```typescript
   const model = aisdk(openai('o4-mini'));
   ```

</Steps>

<Aside type="caution">
  現在は ai-sdk の model provider v2 モジュールに対応しており、Vercel AI SDK v5
  と互換性があります。特別な理由で v1 の model provider
  を使い続けたい場合は、[examples/ai-sdk-v1](https://github.com/openai/openai-agents-js/tree/main/examples/ai-sdk-v1)
  からモジュールをコピーして、プロジェクトに含めてください。
</Aside>

## 例

<Code lang="typescript" code={aiSdkSetupExample} title="AI SDK Setup" />

## プロバイダー メタデータの受け渡し

メッセージにプロバイダー固有のオプションを送る必要がある場合は、`providerMetadata` を介して渡します。値は基盤となる AI SDK モデルへそのまま転送されます。例えば、Agents SDK で次の `providerData`

```ts
providerData: {
  anthropic: {
    cacheControl: {
      type: 'ephemeral';
    }
  }
}
```

は、AI SDK 連携を使用すると

```ts
providerMetadata: {
  anthropic: {
    cacheControl: {
      type: 'ephemeral';
    }
  }
}
```

になります。
