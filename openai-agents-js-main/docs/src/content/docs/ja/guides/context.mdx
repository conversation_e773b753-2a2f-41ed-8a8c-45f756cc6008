---
title: コンテキスト管理
description: Learn how to provide local data via RunContext and expose context to the LLM
---

import { Aside, Code } from '@astrojs/starlight/components';
import localContextExample from '../../../../../../examples/docs/context/localContext.ts?raw';

「コンテキスト」は意味が広い用語です。主に次の 2 つの種類があります。

1. **ローカルコンテキスト**: 実行中にコードがアクセスできるもの。ツールに必要な依存関係やデータ、`onHandoff` のようなコールバック、ライフサイクルフックなど
2. **エージェント/LLM コンテキスト**: 応答を生成するときに言語モデルが参照できるもの

## ローカルコンテキスト

ローカルコンテキストは `RunContext<T>` 型で表現します。状態や依存関係を保持する任意のオブジェクトを作成し、`Runner.run()` に渡します。すべてのツール呼び出しとフックは `RunContext` ラッパーを受け取り、そのオブジェクトを読み取ったり変更したりできます。

<Code
  lang="typescript"
  code={localContextExample}
  title="Local context example"
/>

単一の実行に参加するすべてのエージェント、ツール、フックは、同じ型のコンテキストを使用する必要があります。

ローカルコンテキストの用途例:

- 実行に関するデータ（ユーザー名、ID など）
- ロガーやデータフェッチャーなどの依存関係
- ヘルパー関数

<Aside type="note">
  コンテキストオブジェクトは **LLM**
  に送信されません。純粋にローカルで、自由に読み書きできます。
</Aside>

## エージェント/LLM コンテキスト

LLM を呼び出すとき、モデルが参照できるのは会話履歴から来るデータだけです。追加情報を利用可能にするには、次の選択肢があります。

1. エージェントの `instructions` に追加する — system または developer メッセージとも呼ばれます。これは静的な文字列でも、コンテキストを受け取って文字列を返す関数でもかまいません
2. `Runner.run()` を呼び出すときに `input` に含める。instructions の手法に似ていますが、メッセージを [指揮系統](https://cdn.openai.com/spec/model-spec-2024-05-08.html#follow-the-chain-of-command) の下位に配置できます
3. 関数ツール経由で公開し、LLM がオンデマンドでデータを取得できるようにする
4. リトリーバルまたは Web 検索ツールを使い、ファイル、データベース、Web 由来の関連データに基づいて回答を支える
