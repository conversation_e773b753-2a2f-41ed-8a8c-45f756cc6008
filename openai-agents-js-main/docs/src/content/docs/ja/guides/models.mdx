---
title: モデル
description: Choose and configure language models for your agents
---

import { Code } from '@astrojs/starlight/components';
import modelCustomProviderExample from '../../../../../../examples/docs/models/customProviders.ts?raw';
import setDefaultOpenAIKeyExample from '../../../../../../examples/docs/config/setDefaultOpenAIKey.ts?raw';
import modelSettingsExample from '../../../../../../examples/docs/models/modelSettings.ts?raw';
import promptIdExample from '../../../../../../examples/basic/prompt-id.ts?raw';
import agentWithModelExample from '../../../../../../examples/docs/models/agentWithModel.ts?raw';
import runnerWithModelExample from '../../../../../../examples/docs/models/runnerWithModel.ts?raw';
import setTracingExportApiKeyExample from '../../../../../../examples/docs/config/setTracingExportApiKey.ts?raw';

最終的に、すべての エージェント は LLM を呼び出します。SDK はモデルを 2 つの軽量インターフェースの背後に抽象化します:

- [`Model`](/openai-agents-js/openai/agents/interfaces/model) – 特定の API に対して*1 回*のリクエストを実行する方法を知っています
- [`ModelProvider`](/openai-agents-js/openai/agents/interfaces/modelprovider) – 人間が読めるモデルの**名前**（例: `'gpt‑4o'`）を `Model` インスタンスに解決します

日々の作業では通常、モデルの**名前**と、場合によっては `ModelSettings` にのみ触れます。

<Code
  lang="typescript"
  code={agentWithModelExample}
  title="エージェントごとにモデルを指定"
/>

## デフォルトモデル

`Agent` の初期化時にモデルを指定しない場合、デフォルトモデルが使用されます。現在のデフォルトは [`gpt-4.1`](https://platform.openai.com/docs/models/gpt-4.1) で、エージェントワークフローにおける予測可能性と低レイテンシのバランスに優れています。

[`gpt-5`](https://platform.openai.com/docs/models/gpt-5) などの他モデルに切り替えたい場合、エージェントを設定する方法は 2 つあります。

まず、カスタムモデルを設定していないすべてのエージェントで特定のモデルを一貫して使用したい場合は、エージェントを実行する前に環境変数 `OPENAI_DEFAULT_MODEL` を設定します。

```bash
export OPENAI_DEFAULT_MODEL=gpt-5
node my-awesome-agent.js
```

次に、`Runner` インスタンスにデフォルトモデルを設定できます。エージェントにモデルを設定しない場合、この `Runner` のデフォルトモデルが使用されます。

<Code
  lang="typescript"
  code={runnerWithModelExample}
  title="Runner にデフォルトモデルを設定"
/>

### GPT-5 モデル

GPT-5 の reasoning モデル（[`gpt-5`](https://platform.openai.com/docs/models/gpt-5)、[`gpt-5-mini`](https://platform.openai.com/docs/models/gpt-5-mini)、または [`gpt-5-nano`](https://platform.openai.com/docs/models/gpt-5-nano)）をこの方法で使用する場合、SDK はデフォルトで適切な `modelSettings` を適用します。具体的には、`reasoning.effort` と `verbosity` の両方を `"low"` に設定します。デフォルトモデルの reasoning 努力度を調整するには、独自の `modelSettings` を渡してください:

```ts
import { Agent } from '@openai/agents';

const myAgent = new Agent({
  name: 'My Agent',
  instructions: "You're a helpful agent.",
  modelSettings: {
    reasoning: { effort: 'minimal' },
    text: { verbosity: 'low' },
  },
  // If OPENAI_DEFAULT_MODEL=gpt-5 is set, passing only modelSettings works.
  // It's also fine to pass a GPT-5 model name explicitly:
  // model: 'gpt-5',
});
```

より低レイテンシを求める場合は、[`gpt-5-mini`](https://platform.openai.com/docs/models/gpt-5-mini) または [`gpt-5-nano`](https://platform.openai.com/docs/models/gpt-5-nano) を `reasoning.effort="minimal"` と組み合わせることで、デフォルト設定より高速に応答が返ることが多いです。ただし、Responses API の一部の組み込みツール（ファイル検索や画像生成など）は `"minimal"` の reasoning 努力度をサポートしていないため、この Agents SDK のデフォルトは `"low"` になっています。

### 非 GPT-5 モデル

カスタムの `modelSettings` なしで非 GPT-5 のモデル名を渡した場合、SDK はどのモデルにも互換な汎用の `modelSettings` にフォールバックします。

---

## OpenAI プロバイダー

デフォルトの `ModelProvider` は OpenAI の API を使って名前を解決します。2 つの異なるエンドポイントをサポートします:

| API              | 用途                                                            | `setOpenAIAPI()` を呼ぶ                 |
| ---------------- | --------------------------------------------------------------- | --------------------------------------- |
| Chat Completions | 標準的なチャット & 関数呼び出し                                 | `setOpenAIAPI('chat_completions')`      |
| Responses        | 新しい streaming‑first の生成 API（ツール呼び出し、柔軟な出力） | `setOpenAIAPI('responses')` _(default)_ |

### 認証

<Code
  lang="typescript"
  code={setDefaultOpenAIKeyExample}
  title="デフォルトの OpenAI キーを設定"
/>

ネットワーク設定をカスタマイズする必要がある場合は、`setDefaultOpenAIClient(client)` を介して独自の `OpenAI` クライアントを挿し込むこともできます。

---

## ModelSettings

`ModelSettings` は OpenAI のパラメーターを反映しますが、プロバイダーに依存しません。

| フィールド          | 型                                         | メモ                                                                           |
| ------------------- | ------------------------------------------ | ------------------------------------------------------------------------------ |
| `temperature`       | `number`                                   | クリエイティビティ vs. 決定性                                                  |
| `topP`              | `number`                                   | Nucleus sampling                                                               |
| `frequencyPenalty`  | `number`                                   | 繰り返しトークンの抑制                                                         |
| `presencePenalty`   | `number`                                   | 新しいトークンの促進                                                           |
| `toolChoice`        | `'auto' \| 'required' \| 'none' \| string` | [ツール使用の強制](/openai-agents-js/ja/guides/agents#forcing-tool-use) を参照 |
| `parallelToolCalls` | `boolean`                                  | サポートされる場合、関数呼び出しの並列実行を許可                               |
| `truncation`        | `'auto' \| 'disabled'`                     | トークンの切り捨て戦略                                                         |
| `maxTokens`         | `number`                                   | 応答内の最大トークン数                                                         |
| `store`             | `boolean`                                  | 応答を永続化して取得 / RAG ワークフローで利用                                  |
| `reasoning.effort`  | `'minimal' \| 'low' \| 'medium' \| 'high'` | gpt-5 などの reasoning 努力度                                                  |
| `text.verbosity`    | `'low' \| 'medium' \| 'high'`              | gpt-5 などのテキスト冗長度                                                     |

設定はどちらのレベルにも付与できます:

<Code lang="typescript" code={modelSettingsExample} title="モデル設定" />

`Runner` レベルの設定は、競合するエージェントごとの設定を上書きします。

---

## プロンプト

エージェントは `prompt` パラメーターで構成でき、サーバーに保存されたプロンプト構成を使用してエージェントの動作を制御するよう指定します。現在、このオプションは OpenAI の
[Responses API](https://platform.openai.com/docs/api-reference/responses) を使用する場合にのみサポートされています。

| フィールド  | 型       | メモ                                                                                                                  |
| ----------- | -------- | --------------------------------------------------------------------------------------------------------------------- |
| `promptId`  | `string` | プロンプトの一意な識別子                                                                                              |
| `version`   | `string` | 使用したいプロンプトのバージョン                                                                                      |
| `variables` | `object` | プロンプトに代入する変数のキー/値ペア。値は文字列、またはテキスト・画像・ファイルなどのコンテンツ入力タイプにできます |

<Code
  lang="typescript"
  code={promptIdExample}
  title="プロンプト付きエージェント"
/>

tools や instructions など、追加のエージェント設定は、保存済みプロンプトで設定した値を上書きします。

---

## カスタムモデルプロバイダー

独自のプロバイダーの実装は簡単です。`ModelProvider` と `Model` を実装し、プロバイダーを `Runner` のコンストラクターに渡します:

<Code
  lang="typescript"
  code={modelCustomProviderExample}
  title="最小のカスタムプロバイダー"
/>

---

## トレーシングエクスポーター

OpenAI プロバイダーを使用する場合、API キーを指定して自動トレースエクスポートを有効化できます:

<Code
  lang="typescript"
  code={setTracingExportApiKeyExample}
  title="トレーシングエクスポーター"
/>

これにより、[OpenAI ダッシュボード](https://platform.openai.com/traces) にトレースが送信され、ワークフローの完全な実行グラフを確認できます。

---

## 次のステップ

- [エージェントの実行](/openai-agents-js/ja/guides/running-agents)を参照
- [ツール](/openai-agents-js/ja/guides/tools)でモデルに強力な機能を付与
- 必要に応じて[ガードレール](/openai-agents-js/ja/guides/guardrails)や[トレーシング](/openai-agents-js/ja/guides/tracing)を追加
