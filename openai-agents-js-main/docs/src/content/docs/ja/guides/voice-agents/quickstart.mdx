---
title: クイックスタート
description: Build your first realtime voice assistant using the OpenAI Agents SDK in minutes.
---

import { Steps, Aside, Code } from '@astrojs/starlight/components';
import helloWorldExample from '../../../../../../../examples/docs/voice-agents/helloWorld.ts?raw';
import createAgentExample from '../../../../../../../examples/docs/voice-agents/createAgent.ts?raw';
import multiAgentsExample from '../../../../../../../examples/docs/voice-agents/multiAgents.ts?raw';
import createSessionExample from '../../../../../../../examples/docs/voice-agents/createSession.ts?raw';
import configureSessionExample from '../../../../../../../examples/docs/voice-agents/configureSession.ts?raw';
import handleAudioExample from '../../../../../../../examples/docs/voice-agents/handleAudio.ts?raw';
import defineToolExample from '../../../../../../../examples/docs/voice-agents/defineTool.ts?raw';
import toolApprovalEventExample from '../../../../../../../examples/docs/voice-agents/toolApprovalEvent.ts?raw';
import guardrailsExample from '../../../../../../../examples/docs/voice-agents/guardrails.ts?raw';
import guardrailSettingsExample from '../../../../../../../examples/docs/voice-agents/guardrailSettings.ts?raw';
import audioInterruptedExample from '../../../../../../../examples/docs/voice-agents/audioInterrupted.ts?raw';
import sessionInterruptExample from '../../../../../../../examples/docs/voice-agents/sessionInterrupt.ts?raw';
import sessionHistoryExample from '../../../../../../../examples/docs/voice-agents/sessionHistory.ts?raw';
import historyUpdatedExample from '../../../../../../../examples/docs/voice-agents/historyUpdated.ts?raw';
import updateHistoryExample from '../../../../../../../examples/docs/voice-agents/updateHistory.ts?raw';
import customWebRTCTransportExample from '../../../../../../../examples/docs/voice-agents/customWebRTCTransport.ts?raw';
import websocketSessionExample from '../../../../../../../examples/docs/voice-agents/websocketSession.ts?raw';
import transportEventsExample from '../../../../../../../examples/docs/voice-agents/transportEvents.ts?raw';
import thinClientExample from '../../../../../../../examples/docs/voice-agents/thinClient.ts?raw';

<Steps>

0. **プロジェクトの作成**

   このクイックスタートでは、ブラウザで使える音声エージェントを作成します。新しいプロジェクトを試す場合は、[`Next.js`](https://nextjs.org/docs/getting-started/installation) や [`Vite`](https://vite.dev/guide/installation.html) を試せます。

   ```bash
   npm create vite@latest my-project --template vanilla-ts
   ```

1. **Agents SDK のインストール**

   ```bash
   npm install @openai/agents zod@3
   ```

   代わりに、スタンドアロンのブラウザ用パッケージである `@openai/agents-realtime` をインストールすることもできます。

2. **クライアントのエフェメラルトークンの生成**

   このアプリケーションは ユーザー のブラウザで実行されるため、Realtime API を通じてモデルに安全に接続する方法が必要です。そのために、バックエンド サーバー で生成すべき [ephemeral client key](https://platform.openai.com/docs/guides/realtime#creating-an-ephemeral-token) を使用します。テスト目的であれば、`curl` と通常の OpenAI API キーを使ってキーを生成することもできます。

   ```bash
   curl -X POST https://api.openai.com/v1/realtime/client_secrets \
      -H "Authorization: Bearer $OPENAI_API_KEY" \
      -H "Content-Type: application/json" \
      -d '{
        "session": {
          "type": "realtime",
          "model": "gpt-realtime"
        }
      }'
   ```

   レスポンスには、後で接続に使用できる `client_secret.value` が含まれます。このキーは短時間のみ有効で、再生成が必要になる点に注意してください。

3. **最初のエージェントの作成**

   新しい [`RealtimeAgent`](/openai-agents-js/openai/agents-realtime/classes/realtimeagent/) の作成は、通常の [`Agent`](/openai-agents-js/ja/guides/agents) の作成と非常によく似ています。

   ```typescript
   import { RealtimeAgent } from '@openai/agents-realtime';

   const agent = new RealtimeAgent({
     name: 'Assistant',
     instructions: 'You are a helpful assistant.',
   });
   ```

4. **セッションの作成**

   通常のエージェントと異なり、Voice Agent は会話とモデルへの接続を継続的に処理する `RealtimeSession` の中で常時実行・待機します。このセッションは、音声の処理、中断、その他多くのライフサイクル機能も扱います。これらについては後ほど説明します。

   ```typescript
   import { RealtimeSession } from '@openai/agents-realtime';

   const session = new RealtimeSession(agent, {
     model: 'gpt-realtime',
   });
   ```

   `RealtimeSession` コンストラクターは最初の引数として `agent` を受け取ります。このエージェントが ユーザー が最初に対話できるエージェントになります。

5. **セッションへの接続**

   セッションに接続するには、先ほど生成したクライアントのエフェメラルトークンを渡します。

   ```typescript
   await session.connect({ apiKey: '<client-api-key>' });
   ```

   これにより、ブラウザでは WebRTC を使って Realtime API に接続し、音声の入出力のためにマイクとスピーカーを自動的に設定します。`RealtimeSession` をバックエンド サーバー（たとえば Node.js）で実行している場合、SDK は自動的に接続として WebSocket を使用します。異なるトランスポート層の詳細は、[リアルタイムトランスポート](/openai-agents-js/ja/guides/voice-agents/transport) ガイドをご覧ください。

6. **すべてを組み合わせる**

   <Code lang="typescript" code={helloWorldExample} />

7. **エンジンを始動して話し始める**

   web サーバー を起動し、新しい Realtime Agent のコードを含むページに移動します。マイクへのアクセス許可のリクエストが表示されるはずです。許可すると、エージェントに話しかけられるようになります。

   ```bash
   npm run dev
   ```

</Steps>

## 次のステップ

ここから、独自の音声エージェントの設計と構築を始められます。音声エージェントには通常のエージェントと多くの共通機能がある一方、独自の機能もあります。

- 音声エージェントへの機能追加を学ぶ
  - [ツール](/openai-agents-js/ja/guides/voice-agents/build#tools)
  - [ハンドオフ](/openai-agents-js/ja/guides/voice-agents/build#handoffs)
  - [ガードレール](/openai-agents-js/ja/guides/voice-agents/build#guardrails)
  - [音声の割り込み処理](/openai-agents-js/ja/guides/voice-agents/build#audio-interruptions)
  - [セッション履歴の管理](/openai-agents-js/ja/guides/voice-agents/build#session-history)

- 異なるトランスポート層についてさらに学ぶ
  - [WebRTC](/openai-agents-js/ja/guides/voice-agents/transport#connecting-over-webrtc)
  - [WebSocket](/openai-agents-js/ja/guides/voice-agents/transport#connecting-over-websocket)
  - [独自のトランスポートメカニズムの構築](/openai-agents-js/ja/guides/voice-agents/transport#building-your-own-transport-mechanism)
