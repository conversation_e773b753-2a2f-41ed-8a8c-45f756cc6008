---
title: トラブルシューティング
description: Learn how to troubleshoot issues with the OpenAI Agents SDK.
---

## サポートされる環境

OpenAI Agents SDK は、次のサーバー環境でサポートされています:

- Node.js 22+
- Deno 2.35+
- Bun 1.2.5+

### 限定的なサポート

- **Cloudflare Workers**: Agents SDK は Cloudflare Workers で使用できますが、現在いくつかの制限があります:
  - 現在、SDK では `nodejs_compat` を有効にする必要があります
  - トレースはリクエストの最後に手動でフラッシュする必要があります。詳しくは [トレーシング](/openai-agents-js/ja/guides/tracing#export-loop-lifecycle) を参照してください
  - Cloudflare Workers の `AsyncLocalStorage` サポートが限定的なため、一部のトレースが正確でない可能性があります
- **Browsers**:
  - 現在、ブラウザではトレーシングはサポートされていません
- **v8 isolates**:
  - 適切なブラウザポリフィルを備えたバンドラを使用すれば v8 isolates 向けに SDK をバンドルできるはずですが、トレーシングは動作しません
  - v8 isolates での動作は広範にはテストされていません

## デバッグログ

SDK で問題が発生している場合、デバッグログを有効にして、内部で何が起きているかの詳細情報を得られます。

`DEBUG` 環境変数を `openai-agents:*` に設定してデバッグログを有効にします。

```bash
DEBUG=openai-agents:*
```

または、SDK の特定部分に絞ってデバッグを有効化できます:

- `openai-agents:core` — SDK のメイン実行ロジック用
- `openai-agents:openai` — OpenAI API 呼び出し用
- `openai-agents:realtime` — リアルタイムエージェントのコンポーネント用
