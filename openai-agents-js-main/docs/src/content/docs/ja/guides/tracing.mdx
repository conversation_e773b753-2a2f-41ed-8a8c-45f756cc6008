---
title: トレーシング
description: Learn how to trace your agent runs
---

import { Aside, Code } from '@astrojs/starlight/components';
import customTraceExample from '../../../../../../examples/docs/custom-trace.ts?raw';
import cloudflareWorkers from '../../../../../../examples/docs/tracing/cloudflareWorkers.ts?raw';

Agents SDK には組み込みの トレーシング が含まれており、エージェントの実行中に発生するイベントの包括的な記録を収集します。 LLM の生成、ツール呼び出し、ハンドオフ、ガードレール、さらには発生したカスタムイベントまで対象です。 [Traces ダッシュボード](https://platform.openai.com/traces) を使うと、開発中や本番環境でワークフローをデバッグ、可視化、監視できます。

<Aside type="note">

トレーシングはデフォルトで有効です。トレーシングを無効化する方法は 2 つあります。

1. 環境変数 `OPENAI_AGENTS_DISABLE_TRACING=1` を設定してグローバルに無効化する
2. [`RunConfig.tracingDisabled`](/openai-agents-js/openai/agents-core/type-aliases/runconfig/#tracingdisabled) を `true` に設定して、単一の実行で無効化する

**_OpenAI の API を使用し、Zero Data Retention (ZDR) ポリシーで運用している組織では、トレーシングは利用できません。_**

</Aside>

## エクスポートループのライフサイクル

ほとんどの環境では、トレースは定期的に自動エクスポートされます。ブラウザや Cloudflare Workers では、この機能はデフォルトで無効化されています。キューに溜まりすぎた場合はトレースはエクスポートされますが、定期的なエクスポートは行われません。代わりに、コードのライフサイクルの一部として `getGlobalTraceProvider().forceFlush()` を使って手動でトレースをエクスポートしてください。

たとえば Cloudflare Worker では、コードを `try/catch/finally` ブロックでラップし、`waitUntil` とともに強制フラッシュを使用して、ワーカーが終了する前にトレースがエクスポートされるようにします。

<Code
  lang="typescript"
  code={cloudflareWorkers.replace(/\s+\/\/ @ts-expect-error.*$/m, '')}
  meta="{13}"
/>

## トレースとスパン

- **トレース** は「ワークフロー」の単一のエンドツーエンド処理を表します。スパンで構成されます。トレースには次のプロパティがあります:
  - `workflow_name`: 論理的なワークフローまたはアプリです。例: "Code generation" や "Customer service"
  - `trace_id`: トレースの一意の ID。指定しない場合は自動生成。形式は `trace_<32_alphanumeric>` である必要があります
  - `group_id`: 任意のグループ ID。同じ会話からの複数のトレースを関連付けるために使用します。例: チャットスレッド ID
  - `disabled`: True の場合、トレースは記録されません
  - `metadata`: トレース用の任意のメタデータ
- **スパン** は開始時刻と終了時刻を持つ操作を表します。スパンには次があります:
  - `started_at` と `ended_at` のタイムスタンプ
  - 所属するトレースを表す `trace_id`
  - 親スパンを指す `parent_id`（ある場合）
  - スパンに関する情報である `span_data`。たとえば、`AgentSpanData` はエージェントに関する情報、`GenerationSpanData` は LLM 生成に関する情報など

## デフォルトのトレーシング

デフォルトで SDK は次をトレースします:

- 全体の `run()` または `Runner.run()` は `Trace` にラップされます
- エージェントが実行されるたびに `AgentSpan` にラップされます
- LLM 生成は `GenerationSpan` にラップされます
- 関数ツールの呼び出しはそれぞれ `FunctionSpan` にラップされます
- ガードレールは `GuardrailSpan` にラップされます
- ハンドオフは `HandoffSpan` にラップされます

デフォルトでは、トレース名は "Agent workflow" です。`withTrace` を使う場合はこの名前を設定できますし、[`RunConfig.workflowName`](/openai-agents-js/openai/agents-core/type-aliases/runconfig/#workflowname) で名前やその他のプロパティを設定することもできます。

さらに、[custom trace processors](#custom-tracing-processors) を設定して、他の送信先へトレースをプッシュできます（置き換え、またはセカンダリ送信先として）。

### 音声エージェントのトレーシング

デフォルトの OpenAI Realtime API とともに `RealtimeAgent` と `RealtimeSession` を使用している場合、`RealtimeSession` 側で `tracingDisabled: true` を設定するか、環境変数 `OPENAI_AGENTS_DISABLE_TRACING` を使用して無効化しない限り、トレーシングは Realtime API 側で自動的に行われます。

詳細は [音声エージェントの概要](/openai-agents-js/ja/guides/voice-agents) を確認してください。

## 高レベルのトレース

`run()` への複数回の呼び出しを単一のトレースの一部にしたい場合があります。これはコード全体を `withTrace()` でラップすることで実現できます。

<Code lang="typescript" code={customTraceExample} />

1. `withTrace()` で `run` の 2 回の呼び出しをラップしているため、個々の実行は 2 つのトレースを作成するのではなく、全体のトレースの一部になります。

## トレースの作成

[`withTrace()`](/openai-agents-js/openai/agents-core/functions/withtrace/) 関数を使ってトレースを作成できます。あるいは、`getGlobalTraceProvider().createTrace()` を使用して手動で新しいトレースを作成し、それを `withTrace()` に渡すこともできます。

現在のトレースは [Node.js `AsyncLocalStorage`](https://nodejs.org/api/async_context.html#class-asynclocalstorage) または該当環境のポリフィルによって追跡されます。これは、自動的に並行処理でも機能することを意味します。

## スパンの作成

さまざまな `create*Span()`（例: `createGenerationSpan()`、`createFunctionSpan()` など）メソッドを使ってスパンを作成できます。一般に、手動でスパンを作成する必要はありません。カスタムのスパン情報を追跡するための [`createCustomSpan()`](/openai-agents-js/openai/agents-core/functions/createcustomspan/) 関数も利用できます。

スパンは自動的に現在のトレースの一部となり、[Node.js `AsyncLocalStorage`](https://nodejs.org/api/async_context.html#class-asynclocalstorage) または該当環境のポリフィルで追跡される、最も近い現在のスパンの下にネストされます。

## 機微なデータ

一部のスパンは機微なデータを収集する可能性があります。

`createGenerationSpan()` は LLM 生成の入力/出力を保存し、`createFunctionSpan()` は関数呼び出しの入力/出力を保存します。これらには機微なデータが含まれる場合があるため、[`RunConfig.traceIncludeSensitiveData
`](/openai-agents-js/openai/agents-core/type-aliases/runconfig/#traceincludesensitivedata) でそのデータの取得を無効化できます。

## カスタム トレーシング プロセッサー

トレーシングの高レベルなアーキテクチャは次のとおりです:

- 初期化時に、グローバルな [`TraceProvider`](/openai-agents-js/openai/agents-core/classes/traceprovider) を作成します。これはトレースの作成を担当し、[`getGlobalTraceProvider()`](/openai-agents-js/openai/agents-core/functions/getglobaltraceprovider/) を通じてアクセスできます
- `TraceProvider` を [`BatchTraceProcessor`](/openai-agents-js/openai/agents-core/classes/batchtraceprocessor/) で構成し、バッチで [`OpenAITracingExporter`](/openai-agents-js/openai/agents-openai/classes/openaitracingexporter/) にトレース/スパンを送信します。エクスポーターは OpenAI バックエンドにスパンとトレースをバッチで送信します

このデフォルト構成をカスタマイズして、別のバックエンドへ送信したり、追加のバックエンドに送信したり、エクスポーターの動作を変更したりするには、次の 2 つの方法があります:

1. [`addTraceProcessor()`](/openai-agents-js/openai/agents-core/functions/addtraceprocessor) を使うと、トレースやスパンが準備でき次第受け取る、**追加の** トレースプロセッサーを追加できます。これにより、OpenAI のバックエンドへの送信に加えて独自の処理を実行できます
2. [`setTraceProcessors()`](/openai-agents-js/openai/agents-core/functions/settraceprocessors) を使うと、デフォルトのプロセッサーを独自のトレースプロセッサーで**置き換え**できます。OpenAI バックエンドにトレースを送信するには、その役割を担う `TracingProcessor` を含める必要があります

## 外部トレーシング プロセッサー一覧

- [AgentOps](https://docs.agentops.ai/v2/usage/typescript-sdk#openai-agents-integration)
- [Keywords AI](https://docs.keywordsai.co/integration/development-frameworks/openai-agents-sdk-js)
