---
title: リリースプロセス
description: Learn how we version and release the SDK and recent changes.
---

## バージョニング

このプロジェクトは、`0.Y.Z` 形式を用いた、セマンティックバージョニングを一部変更した方式に従います。先頭の `0` は、SDK がまだ急速に進化していることを示します。各コンポーネントは以下のとおり増分します。

## マイナー（`Y`）バージョン

ベータではない公開インターフェースに対する**破壊的変更**がある場合に、マイナー版の `Y` を上げます。たとえば、`0.0.x` から `0.1.x` への変更には破壊的変更が含まれる可能性があります。

破壊的変更を望まない場合は、プロジェクトで `0.0.x` バージョンに固定することをおすすめします。

## パッチ（`Z`）バージョン

後方互換性を壊さない変更では、`Z` を増分します。

- バグ修正
- 新機能
- 非公開インターフェースの変更
- ベータ機能の更新

## サブパッケージのバージョニング

メインの `@openai/agents` パッケージは、個別に使用できる複数のサブパッケージで構成されています。現時点では各パッケージのバージョンは連動しており、いずれかのパッケージがバージョンアップすると他も同様に上がります。`1.0.0` に移行する際に、この方針を変更する可能性があります。

## 変更履歴

何が変わったかを理解しやすくするため、各サブパッケージごとに変更履歴を生成しています。変更がサブパッケージ内で行われた場合、その該当の変更履歴で詳細を確認する必要があります。

- [`@openai/agents`](https://github.com/openai/openai-agents-js/blob/main/packages/agents/CHANGELOG.md)
- [`@openai/agents-core`](https://github.com/openai/openai-agents-js/blob/main/packages/agents-core/CHANGELOG.md)
- [`@openai/agents-extensions`](https://github.com/openai/openai-agents-js/blob/main/packages/agents-extensions/CHANGELOG.md)
- [`@openai/agents-openai`](https://github.com/openai/openai-agents-js/blob/main/packages/agents-openai/CHANGELOG.md)
- [`@openai/agents-realtime`](https://github.com/openai/openai-agents-js/blob/main/packages/agents-realtime/CHANGELOG.md)
