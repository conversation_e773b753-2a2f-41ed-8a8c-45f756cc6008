---
title: 人間の介入（HITL）
description: Add a human in the loop check for your agent executions
---

import { Aside, Code } from '@astrojs/starlight/components';
import humanInTheLoopExample from '../../../../../../examples/docs/human-in-the-loop/index.ts?raw';
import toolApprovalDefinition from '../../../../../../examples/docs/human-in-the-loop/toolApprovalDefinition.ts?raw';

このガイドでは、SDK に組み込まれた Human in the loop（人間の介入）サポートを使用して、人間の介入に基づいてエージェントの実行を一時停止および再開する方法を説明します。

現在の主なユースケースは、センシティブなツール実行の承認を求めることです。

## 承認リクエスト

`needsApproval` オプションを `true` または boolean を返す非同期関数に設定することで、承認が必要なツールを定義できます。

<Code
  lang="typescript"
  code={toolApprovalDefinition}
  title="ツール承認の定義"
  meta={`{10}`}
/>

### フロー

1. エージェントがツール（複数可）を呼び出すと判断した場合、`needsApproval` を評価してそのツールに承認が必要か確認します
2. 承認が必要な場合、エージェントは承認がすでに許可または拒否されているかを確認します
   - 承認が許可または拒否されていない場合、ツールはツール呼び出しを実行できないことを示す静的メッセージをエージェントに返します
   - 承認 / 拒否が未設定の場合、ツール承認リクエストがトリガーされます
3. エージェントはすべてのツール承認リクエストを収集し、実行を中断します
4. 中断がある場合、[エージェントの実行結果](/openai-agents-js/ja/guides/results) に、保留中のステップを記述する `interruptions` 配列が含まれます。ツール呼び出しに確認が必要な場合、`type: "tool_approval_item"` の `ToolApprovalItem` が表示されます
5. ツール呼び出しを承認または拒否するには、`result.state.approve(interruption)` または `result.state.reject(interruption)` を呼び出します
6. すべての中断を処理したら、`result.state` を `runner.run(agent, state)` に渡して実行を再開します。ここで `agent` は全体の実行をトリガーした元のエージェントです
7. フローは手順 1 から再開します

## 例

以下は、ターミナルで承認を促し、状態を一時的にファイルに保存する Human in the loop（人間の介入）のより完全なフロー例です。

<Code
  lang="typescript"
  code={humanInTheLoopExample}
  title="Human in the loop (人間の介入)"
/>

実行可能なエンドツーエンド版は、[完全なサンプルスクリプト](https://github.com/openai/openai-agents-js/tree/main/examples/agent-patterns/human-in-the-loop.ts) を参照してください。

## 長時間の承認に対処する方法

Human in the loop（人間の介入）のフローは、サーバーを稼働させ続けることなく長時間中断できるように設計されています。リクエストを一旦終了し、後で続行する必要がある場合は、状態をシリアライズして再開できます。

`JSON.stringify(result.state)` を使用して状態をシリアライズし、後で `RunState.fromString(agent, serializedState)` にシリアライズ済みの状態を渡して再開できます。ここで `agent` は全体の実行をトリガーしたエージェントのインスタンスです。

この方法により、シリアライズ済みの状態をデータベースやリクエストと一緒に保存できます。

### 保留タスクのバージョニング

<Aside>
  これは主に、エージェントに変更を加えながらシリアライズ済みの状態を
  長期間保存しようとする場合に該当します。
</Aside>

承認リクエストに時間がかかり、エージェント定義を意味のある形でバージョン管理したい、または Agents SDK のバージョンを上げたい場合は、パッケージエイリアスを使用して 2 つの Agents SDK バージョンを並行してインストールし、独自のブランチングロジックを実装することを現時点では推奨します。

実務上は、自前のコードにバージョン番号を割り当て、シリアライズ済みの状態と一緒に保存し、デシリアライズを適切なコードのバージョンに誘導することを意味します。
