---
title: クイックスタート
description: Create your first AI Agent from scratch
---

import { Steps } from '@astrojs/starlight/components';
import { Code } from '@astrojs/starlight/components';
import quickstartExample from '../../../../../../examples/docs/quickstart/index.ts?raw';

## プロジェクトのセットアップ

<Steps>

1. プロジェクトを作成して npm を初期化します。これは一度だけ行えば十分です。

   ```bash
   mkdir my_project
   cd my_project
   npm init -y
   ```

2. Agents SDK をインストールします。

   ```bash
   npm install @openai/agents zod@3
   ```

3. OpenAI API key を設定します。まだお持ちでない場合は、OpenAI API key を作成するために[こちらの手順](https://platform.openai.com/docs/quickstart#create-and-export-an-api-key)に従ってください。

   ```bash
   export OPENAI_API_KEY=sk-...
   ```

   代わりに、`setDefaultOpenAIKey('<api key>')` を呼び出してプログラムからキーを設定し、トレーシングには `setTracingExportApiKey('<api key>')` を使用できます。
   詳細は[SDK の設定](/openai-agents-js/ja/guides/config)をご覧ください。

</Steps>

## はじめてのエージェントの作成

エージェントは instructions と name で定義します。

```typescript
import { Agent } from '@openai/agents';

const agent = new Agent({
  name: 'History Tutor',
  instructions:
    'You provide assistance with historical queries. Explain important events and context clearly.',
});
```

## はじめてのエージェントの実行

`run` メソッドでエージェントを実行できます。開始したいエージェントと、渡したい入力の両方を指定して実行します。

これにより、その実行中に行われた最終出力とあらゆるアクションを含む実行結果が返されます。

```typescript
import { Agent, run } from '@openai/agents';

const agent = new Agent({
  name: 'History Tutor',
  instructions:
    'You provide assistance with historical queries. Explain important events and context clearly.',
});

const result = await run(agent, 'When did sharks first appear?');

console.log(result.finalOutput);
```

## エージェントへのツール付与

情報の参照やアクションの実行に使えるツールをエージェントに与えられます。

```typescript
import { Agent, tool } from '@openai/agents';

const historyFunFact = tool({
  // The name of the tool will be used by the agent to tell what tool to use.
  name: 'history_fun_fact',
  // The description is used to describe **when** to use the tool by telling it **what** it does.
  description: 'Give a fun fact about a historical event',
  // This tool takes no parameters, so we provide an empty Zod Object.
  parameters: z.object({}),
  execute: async () => {
    // The output will be returned back to the Agent to use
    return 'Sharks are older than trees.';
  },
});

const agent = new Agent({
  name: 'History Tutor',
  instructions:
    'You provide assistance with historical queries. Explain important events and context clearly.',
  // Adding the tool to the agent
  tools: [historyFunFact],
});
```

## エージェントの追加

追加のエージェントを同様に定義して、課題を小さな部分に分割し、エージェントが現在のタスクにより集中できるようにできます。さらに、エージェントごとに model を定義することで、課題に応じて異なるモデルを使い分けられます。

```typescript
const historyTutorAgent = new Agent({
  name: 'History Tutor',
  instructions:
    'You provide assistance with historical queries. Explain important events and context clearly.',
});

const mathTutorAgent = new Agent({
  name: 'Math Tutor',
  instructions:
    'You provide help with math problems. Explain your reasoning at each step and include examples',
});
```

## ハンドオフの定義

複数のエージェントをオーケストレーションするために、エージェントに `handoffs` を定義できます。これにより、エージェントは会話を次のエージェントへ引き継げます。これは実行の過程で自動的に行われます。

```typescript
// Using the Agent.create method to ensures type safety for the final output
const triageAgent = Agent.create({
  name: 'Triage Agent',
  instructions:
    "You determine which agent to use based on the user's homework question",
  handoffs: [historyTutorAgent, mathTutorAgent],
});
```

実行後は、結果の `finalAgent` プロパティを見ることで、どのエージェントが最終応答を生成したかを確認できます。

## エージェントオーケストレーションの実行

Runner は各エージェントの実行、必要なハンドオフ、ツール実行の処理を担当します。

```typescript
import { run } from '@openai/agents';

async function main() {
  const result = await run(triageAgent, 'What is the capital of France?');
  console.log(result.finalOutput);
}

main().catch((err) => console.error(err));
```

## まとめ

これまでの内容を 1 つの完全な例にまとめます。これを `index.js` に配置して実行してください。

<Code lang="typescript" code={quickstartExample} title="Quickstart" />

## トレースの表示

Agents SDK は自動的にトレースを生成します。これにより、エージェントがどのように動作したか、どのツールを呼び出したか、どのエージェントにハンドオフしたかを確認できます。

エージェント実行中に何が起きたかを確認するには、
[OpenAI ダッシュボードの Trace viewer](https://platform.openai.com/traces) に移動します。

## 次のステップ

より複雑なエージェントフローの構築方法を学びましょう:

- [エージェント](/openai-agents-js/ja/guides/agents) の設定について学ぶ
- [エージェントの実行](/openai-agents-js/ja/guides/running-agents) について学ぶ
- [ツール](/openai-agents-js/ja/guides/tools)、[ガードレール](/openai-agents-js/ja/guides/guardrails)、[モデル](/openai-agents-js/ja/guides/models) について学ぶ
