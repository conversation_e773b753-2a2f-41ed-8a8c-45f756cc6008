---
title: マルチエージェント
description: Coordinate the flow between several agents
---

オーケストレーションとは、アプリ内でのエージェントの流れを指します。どのエージェントが、どの順番で実行され、次に何をするかをどのように決めるか、ということです。エージェントをオーケストレーションする主な方法は 2 つあります。

1. LLM に意思決定を任せる方法: LLM の知能を使って、計画・推論し、その上で実行ステップを決めます
2. コードでオーケストレーションする方法: コードでエージェントの流れを決定します

これらのパターンは組み合わせて使えます。以下にそれぞれのトレードオフを説明します。

## LLM によるオーケストレーション

エージェントは、instructions、ツール、ハンドオフを備えた LLM です。つまり、オープンエンドなタスクが与えられた場合、LLM は自律的に計画し、ツールを使って行動やデータ取得を行い、ハンドオフでサブエージェントに委譲できます。たとえば、リサーチエージェントには次のようなツールを備えられます。

- Web 検索でオンライン情報を探す
- ファイル検索と取得で社内データや接続先を検索する
- コンピュータ操作でコンピュータ上のアクションを実行する
- コード実行でデータ分析を行う
- 計画立案、レポート作成などに優れた特化型エージェントへのハンドオフ

このパターンはタスクがオープンエンドで、LLM の知能に依拠したい場合に適しています。重要な戦術は次のとおりです。

1. 良いプロンプトに投資しましょう。利用可能なツール、その使い方、動作すべきパラメーター範囲を明確にします
2. アプリを監視し、反復改善しましょう。問題が起きる箇所を見つけ、プロンプトを改善します
3. エージェントが内省して改善できるようにします。たとえばループで実行して自己批評させる、エラーメッセージを与えて改善させるなど
4. 何でもこなす汎用エージェントではなく、1 つのタスクに特化して優れたエージェントを用意しましょう
5. [evals](https://platform.openai.com/docs/guides/evals) に投資しましょう。エージェントを訓練してタスク達成能力を高められます

## コードによるオーケストレーション

LLM によるオーケストレーションは強力ですが、コードによるオーケストレーションは、速度・コスト・性能の面でより決定的で予測可能になります。よくあるパターンは次のとおりです。

- [structured outputs](https://platform.openai.com/docs/guides/structured-outputs) を使って、コードで検査できる適切な形式のデータを生成する。たとえば、タスクを複数のカテゴリーに分類させ、そのカテゴリーに基づいて次のエージェントを選ぶ
- 複数のエージェントを連結し、前の出力を次の入力に変換する。ブログ記事作成を、リサーチ、アウトライン作成、本文執筆、批評、改善という一連のステップに分解する
- タスクを実行するエージェントと、評価・フィードバックするエージェントを `while` ループで回し、評価者が特定の基準を満たしたと判断するまで続ける
- 複数のエージェントを並列実行する（たとえば JavaScript の基本コンポーネントである `Promise.all` を使用）。相互に依存しない複数タスクがある場合に速度面で有用

`examples/agent-patterns` には多数の code examples を用意しています。
