name: Create Release Tag

on:
  push:
    branches:
      - main

env:
  CI: true

jobs:
  create-tag:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Determine if latest commit is a version bump
        id: check
        run: |
          commit_message=$(git log -1 --pretty=%B)
          echo "Latest commit message: $commit_message"
          if [[ "$commit_message" == "chore: update versions"* ]]; then
            echo "is_version_bump=true" >> "$GITHUB_OUTPUT"
          else
            echo "is_version_bump=false" >> "$GITHUB_OUTPUT"
          fi

      - name: Create and push git tag
        if: steps.check.outputs.is_version_bump == 'true'
        run: |
          version=$(jq -r '.version' packages/agents/package.json)
          tag="v${version}"
          echo "Tag derived from package version: $tag"

          if git rev-parse "refs/tags/$tag" >/dev/null 2>&1; then
            echo "Tag $tag already exists. Skipping."
            exit 0
          fi

          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git tag -a "$tag" -m "Release $tag"
          git push origin "$tag" 