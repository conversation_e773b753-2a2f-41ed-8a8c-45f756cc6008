name: CI

on:
  push:
    branches: [main]
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: "--max_old_space_size=4096"
    strategy:
      matrix:
        # https://nodejs.org/en/about/previous-releases
        # Using 24.3.x due to https://github.com/pnpm/pnpm/issues/9743
        node-version: ['20', '22', '24.3.x']
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.15.0
          run_install: false
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Build all packages
        run: pnpm build
      - name: Run linter
        run: pnpm lint
      - name: Compile examples
        run: pnpm -r build-check
      - name: Run tests
        run: pnpm test
