import { Agent, run, tool } from '@openai/agents';
import { z } from 'zod';
import OpenAI from 'openai';

const getWeatherTool = tool({
    name: 'get_weather',
    description: 'Get the weather for a given city',
    parameters: z.object({ city: z.string() }),
    strict: true,
    async execute({ city }) {
        return `The weather in ${city} is sunny.`;
    },
});

async function main() {
    const client = new OpenAI();
    const conversation = await client.conversations.create({});
    const agent = new Agent({
        name: 'Assistant',
        instructions: 'You are a helpful assistant. be VERY concise.',
        model: "gpt-4o",  // Changed from gpt-5 to gpt-4o since gpt-5 might not be available
        tools: [getWeatherTool],
    });

    const runOptions = { conversationId: conversation.id };
    const result = await run(agent, "What is the weather in NYC?", runOptions);
    console.log(`Output: ${result.finalOutput}`);
}

if (require.main === module) {
    main().catch(console.error);
}
