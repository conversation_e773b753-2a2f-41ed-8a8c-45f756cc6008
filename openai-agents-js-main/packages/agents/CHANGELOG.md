# @openai/agents

## 0.1.0

### Minor Changes

- 80e1fc1: Bump to v0.1 with the following chnages:
  - gpt-5 model support
  - opt-in: gpt-5 as default option
  - ai-sdk model provider v2 migration

  and more

### Patch Changes

- 2260e21: Upgrade openai package to the latest version
- 79a1999: Make docs and comments more consistent using Codex
- Updated dependencies [2260e21]
- Updated dependencies [94f606c]
- Updated dependencies [79a1999]
- Updated dependencies [8cf5356]
- Updated dependencies [47a28ad]
- Updated dependencies [f1e2f60]
- Updated dependencies [42702c0]
- Updated dependencies [ecea142]
- Updated dependencies [2b10adc]
- Updated dependencies [f1e2f60]
- Updated dependencies [8fc01fc]
- Updated dependencies [6f1677c]
  - @openai/agents-openai@0.1.0
  - @openai/agents-core@0.1.0
  - @openai/agents-realtime@0.1.0

## 0.0.17

### Patch Changes

- f825f71: Fix #187 Agent outputType type error with zod@3.25.68+
- 5d247a5: Fix #245 CJS resolution failure
- Updated dependencies [1cd3266]
- Updated dependencies [f825f71]
- Updated dependencies [5d247a5]
  - @openai/agents-core@0.0.17
  - @openai/agents-realtime@0.0.17
  - @openai/agents-openai@0.0.17

## 0.0.16

### Patch Changes

- Updated dependencies [1bb4d86]
- Updated dependencies [a51105b]
- Updated dependencies [b487db1]
- Updated dependencies [4818d5e]
- Updated dependencies [0858c98]
- Updated dependencies [a0b1f3b]
- Updated dependencies [4bfd911]
- Updated dependencies [c42a0a9]
  - @openai/agents-core@0.0.16
  - @openai/agents-openai@0.0.16
  - @openai/agents-realtime@0.0.16

## 0.0.15

### Patch Changes

- Updated dependencies [5f7d0d6]
- Updated dependencies [7b437d9]
- Updated dependencies [b65315f]
- Updated dependencies [0fe38c0]
  - @openai/agents-core@0.0.15
  - @openai/agents-openai@0.0.15
  - @openai/agents-realtime@0.0.15

## 0.0.14

### Patch Changes

- Updated dependencies [08dd469]
- Updated dependencies [d9c4ddf]
- Updated dependencies [b6c7e9d]
- Updated dependencies [fba44d9]
  - @openai/agents-realtime@0.0.14
  - @openai/agents-core@0.0.14
  - @openai/agents-openai@0.0.14

## 0.0.13

### Patch Changes

- Updated dependencies [bd463ef]
- Updated dependencies [9fdecdb]
- Updated dependencies [25241e4]
  - @openai/agents-core@0.0.13
  - @openai/agents-realtime@0.0.13
  - @openai/agents-openai@0.0.13

## 0.0.12

### Patch Changes

- Updated dependencies [af73bfb]
- Updated dependencies [fe5fb97]
- Updated dependencies [ad05c65]
- Updated dependencies [a2f78fe]
- Updated dependencies [886e25a]
- Updated dependencies [d9b94b3]
- Updated dependencies [f6e68f4]
- Updated dependencies [046f8cc]
- Updated dependencies [ed66acf]
- Updated dependencies [40dc0be]
  - @openai/agents-core@0.0.12
  - @openai/agents-openai@0.0.12
  - @openai/agents-realtime@0.0.12

## 0.0.11

### Patch Changes

- Updated dependencies [a60eabe]
- Updated dependencies [07939c0]
- Updated dependencies [a153963]
- Updated dependencies [6e0d1bd]
- Updated dependencies [17077d8]
  - @openai/agents-core@0.0.11
  - @openai/agents-realtime@0.0.11
  - @openai/agents-openai@0.0.11

## 0.0.10

### Patch Changes

- 787968b: fix: use web standard event apis for twilio websocket
- Updated dependencies [c248a7d]
- Updated dependencies [ff63127]
- Updated dependencies [9c60282]
- Updated dependencies [787968b]
- Updated dependencies [f61fd18]
- Updated dependencies [c248a7d]
- Updated dependencies [4adbcb5]
  - @openai/agents-core@0.0.10
  - @openai/agents-realtime@0.0.10
  - @openai/agents-openai@0.0.10

## 0.0.9

### Patch Changes

- Updated dependencies [9028df4]
- Updated dependencies [49bfe25]
- Updated dependencies [ce62f7c]
  - @openai/agents-core@0.0.9
  - @openai/agents-realtime@0.0.9
  - @openai/agents-openai@0.0.9

## 0.0.8

### Patch Changes

- Updated dependencies [6e1d67d]
- Updated dependencies [52eb3f9]
- Updated dependencies [9e6db14]
- Updated dependencies [0565bf1]
- Updated dependencies [fc99390]
- Updated dependencies [52eb3f9]
  - @openai/agents-core@0.0.8
  - @openai/agents-openai@0.0.8
  - @openai/agents-realtime@0.0.8

## 0.0.7

### Patch Changes

- Updated dependencies [0580b9b]
- Updated dependencies [77c603a]
- Updated dependencies [1fccdca]
- Updated dependencies [2fae25c]
  - @openai/agents-core@0.0.7
  - @openai/agents-openai@0.0.7
  - @openai/agents-realtime@0.0.7

## 0.0.6

### Patch Changes

- Updated dependencies [2c6cfb1]
- Updated dependencies [36a401e]
  - @openai/agents-core@0.0.6
  - @openai/agents-openai@0.0.6
  - @openai/agents-realtime@0.0.6

## 0.0.5

### Patch Changes

- Updated dependencies [adeb218]
- Updated dependencies [6e2445a]
- Updated dependencies [ca5cf8b]
- Updated dependencies [cbd4deb]
- Updated dependencies [544ed4b]
  - @openai/agents-openai@0.0.5
  - @openai/agents-realtime@0.0.5
  - @openai/agents-core@0.0.5

## 0.0.4

### Patch Changes

- Updated dependencies [ded675a]
- Updated dependencies [25165df]
- Updated dependencies [6683db0]
- Updated dependencies [78811c6]
- Updated dependencies [426ad73]
  - @openai/agents-openai@0.0.4
  - @openai/agents-core@0.0.4
  - @openai/agents-realtime@0.0.4

## 0.0.3

### Patch Changes

- Updated dependencies [d7fd8dc]
- Updated dependencies [284d0ab]
- Updated dependencies [0474de9]
- Updated dependencies [68ff0ba]
  - @openai/agents-core@0.0.3
  - @openai/agents-openai@0.0.3
  - @openai/agents-realtime@0.0.3

## 0.0.2

### Patch Changes

- Updated dependencies [a2979b6]
- Updated dependencies [b4942fa]
  - @openai/agents-core@0.0.2
  - @openai/agents-openai@0.0.2
  - @openai/agents-realtime@0.0.2

## 0.0.1

### Patch Changes

- aaa6d08: Initial release
- Updated dependencies [aaa6d08]
  - @openai/agents-realtime@0.0.1
  - @openai/agents-openai@0.0.1
  - @openai/agents-core@0.0.1

## 0.0.1-next.0

### Patch Changes

- Initial release
- Updated dependencies
  - @openai/agents-realtime@0.0.1-next.0
  - @openai/agents-openai@0.0.1-next.0
  - @openai/agents-core@0.0.1-next.0
