{"name": "@openai/agents", "repository": "https://github.com/openai/openai-agents-js", "homepage": "https://openai.github.io/openai-agents-js/", "version": "0.1.0", "description": "The OpenAI Agents SDK is a lightweight yet powerful framework for building multi-agent workflows.", "author": "OpenAI <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./realtime": {"types": "./dist/realtime/index.d.ts", "require": "./dist/realtime/index.js", "import": "./dist/realtime/index.mjs"}, "./utils": {"types": "./dist/utils/index.d.ts", "require": "./dist/utils/index.js", "import": "./dist/utils/index.mjs"}}, "scripts": {"prebuild": "tsx ../../scripts/embedMeta.ts", "build": "tsc", "build-check": "tsc --noEmit -p ./tsconfig.test.json"}, "dependencies": {"@openai/agents-core": "workspace:*", "@openai/agents-openai": "workspace:*", "@openai/agents-realtime": "workspace:*", "debug": "^4.4.0", "openai": "^5.16.0"}, "keywords": ["openai", "agents", "ai", "agentic"], "license": "MIT", "devDependencies": {"@types/debug": "^4.1.12", "zod": "^3.25.40"}, "peerDependencies": {"zod": "^3.25.40"}, "files": ["dist"], "typesVersions": {"*": {"realtime": ["dist/realtime/index.d.ts"], "utils": ["dist/utils/index.d.ts"]}}}