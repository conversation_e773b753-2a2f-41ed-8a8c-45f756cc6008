{"name": "@openai/agents-extensions", "repository": "https://github.com/openai/openai-agents-js", "homepage": "https://openai.github.io/openai-agents-js/", "version": "0.1.0", "description": "Extensions for the OpenAI Agents SDK", "author": "OpenAI <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prebuild": "tsx ../../scripts/embedMeta.ts", "build": "tsc", "build-check": "tsc --noEmit -p ./tsconfig.test.json"}, "dependencies": {"@ai-sdk/provider": "^2.0.0", "@types/ws": "^8.18.1", "debug": "^4.4.0"}, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}}, "peerDependencies": {"@openai/agents": "workspace:>=0.0.0", "ws": "^8.18.1", "zod": "^3.25.40"}, "keywords": ["openai", "agents", "ai", "agentic"], "license": "MIT", "devDependencies": {"@openai/agents": "workspace:>=0.0.0", "@types/debug": "^4.1.12", "ws": "^8.18.1", "zod": "^3.25.40"}, "files": ["dist"]}